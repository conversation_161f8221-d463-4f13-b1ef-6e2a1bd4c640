<template>
  <header class="relative z-20 border-b border-gray-800/50 backdrop-blur-md">
    <nav class="container mx-auto px-4 py-4">
      <div class="flex items-center justify-between">
        <!-- Logo -->
        <NuxtLink to="/" class="flex items-center space-x-2 group">
          <div class="w-8 h-8 rounded-full bg-gradient-to-r from-neon-blue to-neon-purple flex items-center justify-center">
            <Icon name="heroicons:sparkles" class="w-5 h-5 text-white" />
          </div>
          <span class="text-xl font-bold neon-glow text-neon-blue group-hover:text-neon-purple transition-colors">
            霓虹夜空
          </span>
        </NuxtLink>

        <!-- 导航菜单 -->
        <div class="hidden md:flex items-center space-x-6">
          <NuxtLink
            to="/stars"
            class="nav-link"
            :class="{ 'active': $route.path.startsWith('/stars') }"
          >
            <Icon name="heroicons:star" class="w-4 h-4" />
            记忆星辰
          </NuxtLink>
          
          <NuxtLink
            to="/galaxy"
            class="nav-link"
            :class="{ 'active': $route.path.startsWith('/galaxy') }"
          >
            <Icon name="heroicons:globe-alt" class="w-4 h-4" />
            遥远银河
          </NuxtLink>
        </div>

        <!-- 用户菜单 -->
        <div class="flex items-center space-x-4">
          <template v-if="user">
            <!-- 已登录状态 -->
            <div class="relative" ref="userMenuRef">
              <button
                @click="toggleUserMenu"
                class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-800/50 transition-colors"
              >
                <div class="w-8 h-8 rounded-full bg-gradient-to-r from-neon-pink to-neon-green flex items-center justify-center">
                  <span class="text-sm font-medium text-white">
                    {{ user.username?.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <Icon name="heroicons:chevron-down" class="w-4 h-4 text-gray-400" />
              </button>

              <!-- 用户下拉菜单 -->
              <div
                v-show="showUserMenu"
                class="absolute right-0 mt-2 w-48 neon-card py-2"
              >
                <NuxtLink
                  to="/profile"
                  class="block px-4 py-2 text-sm hover:bg-gray-800/50 transition-colors"
                  @click="showUserMenu = false"
                >
                  <Icon name="heroicons:user" class="w-4 h-4 inline mr-2" />
                  个人资料
                </NuxtLink>
                <NuxtLink
                  to="/settings"
                  class="block px-4 py-2 text-sm hover:bg-gray-800/50 transition-colors"
                  @click="showUserMenu = false"
                >
                  <Icon name="heroicons:cog-6-tooth" class="w-4 h-4 inline mr-2" />
                  设置
                </NuxtLink>
                <hr class="my-2 border-gray-700" />
                <button
                  @click="logout"
                  class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-800/50 transition-colors text-red-400"
                >
                  <Icon name="heroicons:arrow-right-on-rectangle" class="w-4 h-4 inline mr-2" />
                  退出登录
                </button>
              </div>
            </div>
          </template>
          
          <template v-else>
            <!-- 未登录状态 -->
            <NuxtLink to="/auth/login" class="btn-neon">
              登录
            </NuxtLink>
          </template>

          <!-- 移动端菜单按钮 -->
          <button
            @click="toggleMobileMenu"
            class="md:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors"
          >
            <Icon name="heroicons:bars-3" class="w-6 h-6" />
          </button>
        </div>
      </div>

      <!-- 移动端菜单 -->
      <div
        v-show="showMobileMenu"
        class="md:hidden mt-4 pt-4 border-t border-gray-800/50"
      >
        <div class="space-y-2">
          <NuxtLink
            to="/stars"
            class="block nav-link"
            @click="showMobileMenu = false"
          >
            <Icon name="heroicons:star" class="w-4 h-4" />
            记忆星辰
          </NuxtLink>
          
          <NuxtLink
            to="/galaxy"
            class="block nav-link"
            @click="showMobileMenu = false"
          >
            <Icon name="heroicons:globe-alt" class="w-4 h-4" />
            遥远银河
          </NuxtLink>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '~/stores/auth'
import { onClickOutside } from '@vueuse/core'

const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const { logout } = authStore

const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const userMenuRef = ref(null)

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 点击外部关闭用户菜单
onClickOutside(userMenuRef, () => {
  showUserMenu.value = false
})
</script>

<style scoped>
.nav-link {
  @apply flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800/50 transition-all duration-200;
}

.nav-link.active {
  @apply text-neon-blue bg-gray-800/30;
}

.nav-link:hover {
  @apply shadow-neon;
}
</style>
