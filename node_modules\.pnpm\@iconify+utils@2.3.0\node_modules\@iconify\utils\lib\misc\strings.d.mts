/**
 * Convert string to camelCase
 */
declare function camelize(str: string): string;
/**
 * Convert string to PascaleCase
 */
declare function pascalize(str: string): string;
/**
 * Convert camelCase string to kebab-case
 */
declare function camelToKebab(key: string): string;
/**
 * Convert camelCase string to snake-case
 */
declare function snakelize(str: string): string;

export { camelToKebab, camelize, pascalize, snakelize };
