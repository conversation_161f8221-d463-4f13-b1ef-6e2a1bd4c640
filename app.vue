<template>
  <div class="min-h-screen bg-starfield">
    <!-- 星空背景 -->
    <StarField />

    <!-- 主要内容 -->
    <NuxtRouteAnnouncer />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>

    <!-- 全局通知 -->
    <NotificationContainer />
  </div>
</template>

<script setup>
// 设置页面元数据
useHead({
  title: '霓虹夜空 - 记录情绪，寻找共鸣',
  meta: [
    { name: 'description', content: '一个私密的个人情绪记录与封存空间，在这里记录你的记忆星辰，寻找遥远的共鸣。' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { property: 'og:title', content: '霓虹夜空' },
    { property: 'og:description', content: '记录情绪，寻找共鸣的私密空间' },
    { property: 'og:type', content: 'website' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
  ]
})

// 设置颜色模式为深色
const colorMode = useColorMode()
colorMode.preference = 'dark'
</script>

<style>
html {
  font-family: 'Inter', sans-serif;
}
</style>
