
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model Session
 * 
 */
export type Session = $Result.DefaultSelection<Prisma.$SessionPayload>
/**
 * Model Star
 * 
 */
export type Star = $Result.DefaultSelection<Prisma.$StarPayload>
/**
 * Model Resonance
 * 
 */
export type Resonance = $Result.DefaultSelection<Prisma.$ResonancePayload>
/**
 * Model GalaxyEmotion
 * 
 */
export type GalaxyEmotion = $Result.DefaultSelection<Prisma.$GalaxyEmotionPayload>
/**
 * Model GalaxyResonance
 * 
 */
export type GalaxyResonance = $Result.DefaultSelection<Prisma.$GalaxyResonancePayload>

/**
 * Enums
 */
export namespace $Enums {
  export const StarType: {
  TEXT: 'TEXT',
  IMAGE: 'IMAGE',
  AUDIO: 'AUDIO',
  MIXED: 'MIXED'
};

export type StarType = (typeof StarType)[keyof typeof StarType]


export const Emotion: {
  JOY: 'JOY',
  SADNESS: 'SADNESS',
  ANGER: 'ANGER',
  FEAR: 'FEAR',
  LOVE: 'LOVE',
  HOPE: 'HOPE',
  NOSTALGIA: 'NOSTALGIA',
  ANXIETY: 'ANXIETY',
  PEACE: 'PEACE',
  NEUTRAL: 'NEUTRAL'
};

export type Emotion = (typeof Emotion)[keyof typeof Emotion]


export const ResonanceType: {
  PULSE: 'PULSE',
  WARMTH: 'WARMTH',
  COMFORT: 'COMFORT',
  UNDERSTANDING: 'UNDERSTANDING'
};

export type ResonanceType = (typeof ResonanceType)[keyof typeof ResonanceType]

}

export type StarType = $Enums.StarType

export const StarType: typeof $Enums.StarType

export type Emotion = $Enums.Emotion

export const Emotion: typeof $Enums.Emotion

export type ResonanceType = $Enums.ResonanceType

export const ResonanceType: typeof $Enums.ResonanceType

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.session`: Exposes CRUD operations for the **Session** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Sessions
    * const sessions = await prisma.session.findMany()
    * ```
    */
  get session(): Prisma.SessionDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.star`: Exposes CRUD operations for the **Star** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Stars
    * const stars = await prisma.star.findMany()
    * ```
    */
  get star(): Prisma.StarDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.resonance`: Exposes CRUD operations for the **Resonance** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Resonances
    * const resonances = await prisma.resonance.findMany()
    * ```
    */
  get resonance(): Prisma.ResonanceDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.galaxyEmotion`: Exposes CRUD operations for the **GalaxyEmotion** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more GalaxyEmotions
    * const galaxyEmotions = await prisma.galaxyEmotion.findMany()
    * ```
    */
  get galaxyEmotion(): Prisma.GalaxyEmotionDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.galaxyResonance`: Exposes CRUD operations for the **GalaxyResonance** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more GalaxyResonances
    * const galaxyResonances = await prisma.galaxyResonance.findMany()
    * ```
    */
  get galaxyResonance(): Prisma.GalaxyResonanceDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.11.1
   * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    Session: 'Session',
    Star: 'Star',
    Resonance: 'Resonance',
    GalaxyEmotion: 'GalaxyEmotion',
    GalaxyResonance: 'GalaxyResonance'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "user" | "session" | "star" | "resonance" | "galaxyEmotion" | "galaxyResonance"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      Session: {
        payload: Prisma.$SessionPayload<ExtArgs>
        fields: Prisma.SessionFieldRefs
        operations: {
          findUnique: {
            args: Prisma.SessionFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.SessionFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>
          }
          findFirst: {
            args: Prisma.SessionFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.SessionFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>
          }
          findMany: {
            args: Prisma.SessionFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>[]
          }
          create: {
            args: Prisma.SessionCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>
          }
          createMany: {
            args: Prisma.SessionCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.SessionCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>[]
          }
          delete: {
            args: Prisma.SessionDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>
          }
          update: {
            args: Prisma.SessionUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>
          }
          deleteMany: {
            args: Prisma.SessionDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.SessionUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.SessionUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>[]
          }
          upsert: {
            args: Prisma.SessionUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionPayload>
          }
          aggregate: {
            args: Prisma.SessionAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateSession>
          }
          groupBy: {
            args: Prisma.SessionGroupByArgs<ExtArgs>
            result: $Utils.Optional<SessionGroupByOutputType>[]
          }
          count: {
            args: Prisma.SessionCountArgs<ExtArgs>
            result: $Utils.Optional<SessionCountAggregateOutputType> | number
          }
        }
      }
      Star: {
        payload: Prisma.$StarPayload<ExtArgs>
        fields: Prisma.StarFieldRefs
        operations: {
          findUnique: {
            args: Prisma.StarFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.StarFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>
          }
          findFirst: {
            args: Prisma.StarFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.StarFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>
          }
          findMany: {
            args: Prisma.StarFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>[]
          }
          create: {
            args: Prisma.StarCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>
          }
          createMany: {
            args: Prisma.StarCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.StarCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>[]
          }
          delete: {
            args: Prisma.StarDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>
          }
          update: {
            args: Prisma.StarUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>
          }
          deleteMany: {
            args: Prisma.StarDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.StarUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.StarUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>[]
          }
          upsert: {
            args: Prisma.StarUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StarPayload>
          }
          aggregate: {
            args: Prisma.StarAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateStar>
          }
          groupBy: {
            args: Prisma.StarGroupByArgs<ExtArgs>
            result: $Utils.Optional<StarGroupByOutputType>[]
          }
          count: {
            args: Prisma.StarCountArgs<ExtArgs>
            result: $Utils.Optional<StarCountAggregateOutputType> | number
          }
        }
      }
      Resonance: {
        payload: Prisma.$ResonancePayload<ExtArgs>
        fields: Prisma.ResonanceFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ResonanceFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ResonanceFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>
          }
          findFirst: {
            args: Prisma.ResonanceFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ResonanceFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>
          }
          findMany: {
            args: Prisma.ResonanceFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>[]
          }
          create: {
            args: Prisma.ResonanceCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>
          }
          createMany: {
            args: Prisma.ResonanceCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ResonanceCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>[]
          }
          delete: {
            args: Prisma.ResonanceDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>
          }
          update: {
            args: Prisma.ResonanceUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>
          }
          deleteMany: {
            args: Prisma.ResonanceDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ResonanceUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ResonanceUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>[]
          }
          upsert: {
            args: Prisma.ResonanceUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ResonancePayload>
          }
          aggregate: {
            args: Prisma.ResonanceAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateResonance>
          }
          groupBy: {
            args: Prisma.ResonanceGroupByArgs<ExtArgs>
            result: $Utils.Optional<ResonanceGroupByOutputType>[]
          }
          count: {
            args: Prisma.ResonanceCountArgs<ExtArgs>
            result: $Utils.Optional<ResonanceCountAggregateOutputType> | number
          }
        }
      }
      GalaxyEmotion: {
        payload: Prisma.$GalaxyEmotionPayload<ExtArgs>
        fields: Prisma.GalaxyEmotionFieldRefs
        operations: {
          findUnique: {
            args: Prisma.GalaxyEmotionFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.GalaxyEmotionFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>
          }
          findFirst: {
            args: Prisma.GalaxyEmotionFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.GalaxyEmotionFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>
          }
          findMany: {
            args: Prisma.GalaxyEmotionFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>[]
          }
          create: {
            args: Prisma.GalaxyEmotionCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>
          }
          createMany: {
            args: Prisma.GalaxyEmotionCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.GalaxyEmotionCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>[]
          }
          delete: {
            args: Prisma.GalaxyEmotionDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>
          }
          update: {
            args: Prisma.GalaxyEmotionUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>
          }
          deleteMany: {
            args: Prisma.GalaxyEmotionDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.GalaxyEmotionUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.GalaxyEmotionUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>[]
          }
          upsert: {
            args: Prisma.GalaxyEmotionUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyEmotionPayload>
          }
          aggregate: {
            args: Prisma.GalaxyEmotionAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateGalaxyEmotion>
          }
          groupBy: {
            args: Prisma.GalaxyEmotionGroupByArgs<ExtArgs>
            result: $Utils.Optional<GalaxyEmotionGroupByOutputType>[]
          }
          count: {
            args: Prisma.GalaxyEmotionCountArgs<ExtArgs>
            result: $Utils.Optional<GalaxyEmotionCountAggregateOutputType> | number
          }
        }
      }
      GalaxyResonance: {
        payload: Prisma.$GalaxyResonancePayload<ExtArgs>
        fields: Prisma.GalaxyResonanceFieldRefs
        operations: {
          findUnique: {
            args: Prisma.GalaxyResonanceFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.GalaxyResonanceFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>
          }
          findFirst: {
            args: Prisma.GalaxyResonanceFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.GalaxyResonanceFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>
          }
          findMany: {
            args: Prisma.GalaxyResonanceFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>[]
          }
          create: {
            args: Prisma.GalaxyResonanceCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>
          }
          createMany: {
            args: Prisma.GalaxyResonanceCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.GalaxyResonanceCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>[]
          }
          delete: {
            args: Prisma.GalaxyResonanceDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>
          }
          update: {
            args: Prisma.GalaxyResonanceUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>
          }
          deleteMany: {
            args: Prisma.GalaxyResonanceDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.GalaxyResonanceUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.GalaxyResonanceUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>[]
          }
          upsert: {
            args: Prisma.GalaxyResonanceUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GalaxyResonancePayload>
          }
          aggregate: {
            args: Prisma.GalaxyResonanceAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateGalaxyResonance>
          }
          groupBy: {
            args: Prisma.GalaxyResonanceGroupByArgs<ExtArgs>
            result: $Utils.Optional<GalaxyResonanceGroupByOutputType>[]
          }
          count: {
            args: Prisma.GalaxyResonanceCountArgs<ExtArgs>
            result: $Utils.Optional<GalaxyResonanceCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    user?: UserOmit
    session?: SessionOmit
    star?: StarOmit
    resonance?: ResonanceOmit
    galaxyEmotion?: GalaxyEmotionOmit
    galaxyResonance?: GalaxyResonanceOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    stars: number
    resonances: number
    sessions: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    stars?: boolean | UserCountOutputTypeCountStarsArgs
    resonances?: boolean | UserCountOutputTypeCountResonancesArgs
    sessions?: boolean | UserCountOutputTypeCountSessionsArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountStarsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StarWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountResonancesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ResonanceWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountSessionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SessionWhereInput
  }


  /**
   * Count Type StarCountOutputType
   */

  export type StarCountOutputType = {
    resonances: number
  }

  export type StarCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    resonances?: boolean | StarCountOutputTypeCountResonancesArgs
  }

  // Custom InputTypes
  /**
   * StarCountOutputType without action
   */
  export type StarCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StarCountOutputType
     */
    select?: StarCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * StarCountOutputType without action
   */
  export type StarCountOutputTypeCountResonancesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ResonanceWhereInput
  }


  /**
   * Count Type GalaxyEmotionCountOutputType
   */

  export type GalaxyEmotionCountOutputType = {
    resonances: number
  }

  export type GalaxyEmotionCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    resonances?: boolean | GalaxyEmotionCountOutputTypeCountResonancesArgs
  }

  // Custom InputTypes
  /**
   * GalaxyEmotionCountOutputType without action
   */
  export type GalaxyEmotionCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotionCountOutputType
     */
    select?: GalaxyEmotionCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * GalaxyEmotionCountOutputType without action
   */
  export type GalaxyEmotionCountOutputTypeCountResonancesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GalaxyResonanceWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserMinAggregateOutputType = {
    id: string | null
    email: string | null
    username: string | null
    password: string | null
    avatar: string | null
    bio: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: string | null
    email: string | null
    username: string | null
    password: string | null
    avatar: string | null
    bio: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    email: number
    username: number
    password: number
    avatar: number
    bio: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserMinAggregateInputType = {
    id?: true
    email?: true
    username?: true
    password?: true
    avatar?: true
    bio?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    email?: true
    username?: true
    password?: true
    avatar?: true
    bio?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    email?: true
    username?: true
    password?: true
    avatar?: true
    bio?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: string
    email: string
    username: string
    password: string
    avatar: string | null
    bio: string | null
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    username?: boolean
    password?: boolean
    avatar?: boolean
    bio?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    stars?: boolean | User$starsArgs<ExtArgs>
    resonances?: boolean | User$resonancesArgs<ExtArgs>
    sessions?: boolean | User$sessionsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    username?: boolean
    password?: boolean
    avatar?: boolean
    bio?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    username?: boolean
    password?: boolean
    avatar?: boolean
    bio?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    email?: boolean
    username?: boolean
    password?: boolean
    avatar?: boolean
    bio?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "email" | "username" | "password" | "avatar" | "bio" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    stars?: boolean | User$starsArgs<ExtArgs>
    resonances?: boolean | User$resonancesArgs<ExtArgs>
    sessions?: boolean | User$sessionsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type UserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      stars: Prisma.$StarPayload<ExtArgs>[]
      resonances: Prisma.$ResonancePayload<ExtArgs>[]
      sessions: Prisma.$SessionPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string
      username: string
      password: string
      avatar: string | null
      bio: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const userWithIdOnly = await prisma.user.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    stars<T extends User$starsArgs<ExtArgs> = {}>(args?: Subset<T, User$starsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    resonances<T extends User$resonancesArgs<ExtArgs> = {}>(args?: Subset<T, User$resonancesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    sessions<T extends User$sessionsArgs<ExtArgs> = {}>(args?: Subset<T, User$sessionsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly username: FieldRef<"User", 'String'>
    readonly password: FieldRef<"User", 'String'>
    readonly avatar: FieldRef<"User", 'String'>
    readonly bio: FieldRef<"User", 'String'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User updateManyAndReturn
   */
  export type UserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.stars
   */
  export type User$starsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    where?: StarWhereInput
    orderBy?: StarOrderByWithRelationInput | StarOrderByWithRelationInput[]
    cursor?: StarWhereUniqueInput
    take?: number
    skip?: number
    distinct?: StarScalarFieldEnum | StarScalarFieldEnum[]
  }

  /**
   * User.resonances
   */
  export type User$resonancesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    where?: ResonanceWhereInput
    orderBy?: ResonanceOrderByWithRelationInput | ResonanceOrderByWithRelationInput[]
    cursor?: ResonanceWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ResonanceScalarFieldEnum | ResonanceScalarFieldEnum[]
  }

  /**
   * User.sessions
   */
  export type User$sessionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    where?: SessionWhereInput
    orderBy?: SessionOrderByWithRelationInput | SessionOrderByWithRelationInput[]
    cursor?: SessionWhereUniqueInput
    take?: number
    skip?: number
    distinct?: SessionScalarFieldEnum | SessionScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model Session
   */

  export type AggregateSession = {
    _count: SessionCountAggregateOutputType | null
    _min: SessionMinAggregateOutputType | null
    _max: SessionMaxAggregateOutputType | null
  }

  export type SessionMinAggregateOutputType = {
    id: string | null
    userId: string | null
    token: string | null
    expiresAt: Date | null
    createdAt: Date | null
  }

  export type SessionMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    token: string | null
    expiresAt: Date | null
    createdAt: Date | null
  }

  export type SessionCountAggregateOutputType = {
    id: number
    userId: number
    token: number
    expiresAt: number
    createdAt: number
    _all: number
  }


  export type SessionMinAggregateInputType = {
    id?: true
    userId?: true
    token?: true
    expiresAt?: true
    createdAt?: true
  }

  export type SessionMaxAggregateInputType = {
    id?: true
    userId?: true
    token?: true
    expiresAt?: true
    createdAt?: true
  }

  export type SessionCountAggregateInputType = {
    id?: true
    userId?: true
    token?: true
    expiresAt?: true
    createdAt?: true
    _all?: true
  }

  export type SessionAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Session to aggregate.
     */
    where?: SessionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Sessions to fetch.
     */
    orderBy?: SessionOrderByWithRelationInput | SessionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: SessionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Sessions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Sessions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Sessions
    **/
    _count?: true | SessionCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: SessionMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: SessionMaxAggregateInputType
  }

  export type GetSessionAggregateType<T extends SessionAggregateArgs> = {
        [P in keyof T & keyof AggregateSession]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateSession[P]>
      : GetScalarType<T[P], AggregateSession[P]>
  }




  export type SessionGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SessionWhereInput
    orderBy?: SessionOrderByWithAggregationInput | SessionOrderByWithAggregationInput[]
    by: SessionScalarFieldEnum[] | SessionScalarFieldEnum
    having?: SessionScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: SessionCountAggregateInputType | true
    _min?: SessionMinAggregateInputType
    _max?: SessionMaxAggregateInputType
  }

  export type SessionGroupByOutputType = {
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
    _count: SessionCountAggregateOutputType | null
    _min: SessionMinAggregateOutputType | null
    _max: SessionMaxAggregateOutputType | null
  }

  type GetSessionGroupByPayload<T extends SessionGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<SessionGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof SessionGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], SessionGroupByOutputType[P]>
            : GetScalarType<T[P], SessionGroupByOutputType[P]>
        }
      >
    >


  export type SessionSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    token?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["session"]>

  export type SessionSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    token?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["session"]>

  export type SessionSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    token?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["session"]>

  export type SessionSelectScalar = {
    id?: boolean
    userId?: boolean
    token?: boolean
    expiresAt?: boolean
    createdAt?: boolean
  }

  export type SessionOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "token" | "expiresAt" | "createdAt", ExtArgs["result"]["session"]>
  export type SessionInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type SessionIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type SessionIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $SessionPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Session"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      token: string
      expiresAt: Date
      createdAt: Date
    }, ExtArgs["result"]["session"]>
    composites: {}
  }

  type SessionGetPayload<S extends boolean | null | undefined | SessionDefaultArgs> = $Result.GetResult<Prisma.$SessionPayload, S>

  type SessionCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<SessionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: SessionCountAggregateInputType | true
    }

  export interface SessionDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Session'], meta: { name: 'Session' } }
    /**
     * Find zero or one Session that matches the filter.
     * @param {SessionFindUniqueArgs} args - Arguments to find a Session
     * @example
     * // Get one Session
     * const session = await prisma.session.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends SessionFindUniqueArgs>(args: SelectSubset<T, SessionFindUniqueArgs<ExtArgs>>): Prisma__SessionClient<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Session that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {SessionFindUniqueOrThrowArgs} args - Arguments to find a Session
     * @example
     * // Get one Session
     * const session = await prisma.session.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends SessionFindUniqueOrThrowArgs>(args: SelectSubset<T, SessionFindUniqueOrThrowArgs<ExtArgs>>): Prisma__SessionClient<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Session that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionFindFirstArgs} args - Arguments to find a Session
     * @example
     * // Get one Session
     * const session = await prisma.session.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends SessionFindFirstArgs>(args?: SelectSubset<T, SessionFindFirstArgs<ExtArgs>>): Prisma__SessionClient<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Session that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionFindFirstOrThrowArgs} args - Arguments to find a Session
     * @example
     * // Get one Session
     * const session = await prisma.session.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends SessionFindFirstOrThrowArgs>(args?: SelectSubset<T, SessionFindFirstOrThrowArgs<ExtArgs>>): Prisma__SessionClient<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Sessions that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Sessions
     * const sessions = await prisma.session.findMany()
     * 
     * // Get first 10 Sessions
     * const sessions = await prisma.session.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const sessionWithIdOnly = await prisma.session.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends SessionFindManyArgs>(args?: SelectSubset<T, SessionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Session.
     * @param {SessionCreateArgs} args - Arguments to create a Session.
     * @example
     * // Create one Session
     * const Session = await prisma.session.create({
     *   data: {
     *     // ... data to create a Session
     *   }
     * })
     * 
     */
    create<T extends SessionCreateArgs>(args: SelectSubset<T, SessionCreateArgs<ExtArgs>>): Prisma__SessionClient<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Sessions.
     * @param {SessionCreateManyArgs} args - Arguments to create many Sessions.
     * @example
     * // Create many Sessions
     * const session = await prisma.session.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends SessionCreateManyArgs>(args?: SelectSubset<T, SessionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Sessions and returns the data saved in the database.
     * @param {SessionCreateManyAndReturnArgs} args - Arguments to create many Sessions.
     * @example
     * // Create many Sessions
     * const session = await prisma.session.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Sessions and only return the `id`
     * const sessionWithIdOnly = await prisma.session.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends SessionCreateManyAndReturnArgs>(args?: SelectSubset<T, SessionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Session.
     * @param {SessionDeleteArgs} args - Arguments to delete one Session.
     * @example
     * // Delete one Session
     * const Session = await prisma.session.delete({
     *   where: {
     *     // ... filter to delete one Session
     *   }
     * })
     * 
     */
    delete<T extends SessionDeleteArgs>(args: SelectSubset<T, SessionDeleteArgs<ExtArgs>>): Prisma__SessionClient<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Session.
     * @param {SessionUpdateArgs} args - Arguments to update one Session.
     * @example
     * // Update one Session
     * const session = await prisma.session.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends SessionUpdateArgs>(args: SelectSubset<T, SessionUpdateArgs<ExtArgs>>): Prisma__SessionClient<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Sessions.
     * @param {SessionDeleteManyArgs} args - Arguments to filter Sessions to delete.
     * @example
     * // Delete a few Sessions
     * const { count } = await prisma.session.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends SessionDeleteManyArgs>(args?: SelectSubset<T, SessionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Sessions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Sessions
     * const session = await prisma.session.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends SessionUpdateManyArgs>(args: SelectSubset<T, SessionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Sessions and returns the data updated in the database.
     * @param {SessionUpdateManyAndReturnArgs} args - Arguments to update many Sessions.
     * @example
     * // Update many Sessions
     * const session = await prisma.session.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Sessions and only return the `id`
     * const sessionWithIdOnly = await prisma.session.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends SessionUpdateManyAndReturnArgs>(args: SelectSubset<T, SessionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Session.
     * @param {SessionUpsertArgs} args - Arguments to update or create a Session.
     * @example
     * // Update or create a Session
     * const session = await prisma.session.upsert({
     *   create: {
     *     // ... data to create a Session
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Session we want to update
     *   }
     * })
     */
    upsert<T extends SessionUpsertArgs>(args: SelectSubset<T, SessionUpsertArgs<ExtArgs>>): Prisma__SessionClient<$Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Sessions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionCountArgs} args - Arguments to filter Sessions to count.
     * @example
     * // Count the number of Sessions
     * const count = await prisma.session.count({
     *   where: {
     *     // ... the filter for the Sessions we want to count
     *   }
     * })
    **/
    count<T extends SessionCountArgs>(
      args?: Subset<T, SessionCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], SessionCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Session.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends SessionAggregateArgs>(args: Subset<T, SessionAggregateArgs>): Prisma.PrismaPromise<GetSessionAggregateType<T>>

    /**
     * Group by Session.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends SessionGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: SessionGroupByArgs['orderBy'] }
        : { orderBy?: SessionGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, SessionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSessionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Session model
   */
  readonly fields: SessionFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Session.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__SessionClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Session model
   */
  interface SessionFieldRefs {
    readonly id: FieldRef<"Session", 'String'>
    readonly userId: FieldRef<"Session", 'String'>
    readonly token: FieldRef<"Session", 'String'>
    readonly expiresAt: FieldRef<"Session", 'DateTime'>
    readonly createdAt: FieldRef<"Session", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Session findUnique
   */
  export type SessionFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * Filter, which Session to fetch.
     */
    where: SessionWhereUniqueInput
  }

  /**
   * Session findUniqueOrThrow
   */
  export type SessionFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * Filter, which Session to fetch.
     */
    where: SessionWhereUniqueInput
  }

  /**
   * Session findFirst
   */
  export type SessionFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * Filter, which Session to fetch.
     */
    where?: SessionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Sessions to fetch.
     */
    orderBy?: SessionOrderByWithRelationInput | SessionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Sessions.
     */
    cursor?: SessionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Sessions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Sessions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Sessions.
     */
    distinct?: SessionScalarFieldEnum | SessionScalarFieldEnum[]
  }

  /**
   * Session findFirstOrThrow
   */
  export type SessionFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * Filter, which Session to fetch.
     */
    where?: SessionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Sessions to fetch.
     */
    orderBy?: SessionOrderByWithRelationInput | SessionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Sessions.
     */
    cursor?: SessionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Sessions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Sessions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Sessions.
     */
    distinct?: SessionScalarFieldEnum | SessionScalarFieldEnum[]
  }

  /**
   * Session findMany
   */
  export type SessionFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * Filter, which Sessions to fetch.
     */
    where?: SessionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Sessions to fetch.
     */
    orderBy?: SessionOrderByWithRelationInput | SessionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Sessions.
     */
    cursor?: SessionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Sessions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Sessions.
     */
    skip?: number
    distinct?: SessionScalarFieldEnum | SessionScalarFieldEnum[]
  }

  /**
   * Session create
   */
  export type SessionCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * The data needed to create a Session.
     */
    data: XOR<SessionCreateInput, SessionUncheckedCreateInput>
  }

  /**
   * Session createMany
   */
  export type SessionCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Sessions.
     */
    data: SessionCreateManyInput | SessionCreateManyInput[]
  }

  /**
   * Session createManyAndReturn
   */
  export type SessionCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * The data used to create many Sessions.
     */
    data: SessionCreateManyInput | SessionCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Session update
   */
  export type SessionUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * The data needed to update a Session.
     */
    data: XOR<SessionUpdateInput, SessionUncheckedUpdateInput>
    /**
     * Choose, which Session to update.
     */
    where: SessionWhereUniqueInput
  }

  /**
   * Session updateMany
   */
  export type SessionUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Sessions.
     */
    data: XOR<SessionUpdateManyMutationInput, SessionUncheckedUpdateManyInput>
    /**
     * Filter which Sessions to update
     */
    where?: SessionWhereInput
    /**
     * Limit how many Sessions to update.
     */
    limit?: number
  }

  /**
   * Session updateManyAndReturn
   */
  export type SessionUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * The data used to update Sessions.
     */
    data: XOR<SessionUpdateManyMutationInput, SessionUncheckedUpdateManyInput>
    /**
     * Filter which Sessions to update
     */
    where?: SessionWhereInput
    /**
     * Limit how many Sessions to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Session upsert
   */
  export type SessionUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * The filter to search for the Session to update in case it exists.
     */
    where: SessionWhereUniqueInput
    /**
     * In case the Session found by the `where` argument doesn't exist, create a new Session with this data.
     */
    create: XOR<SessionCreateInput, SessionUncheckedCreateInput>
    /**
     * In case the Session was found with the provided `where` argument, update it with this data.
     */
    update: XOR<SessionUpdateInput, SessionUncheckedUpdateInput>
  }

  /**
   * Session delete
   */
  export type SessionDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
    /**
     * Filter which Session to delete.
     */
    where: SessionWhereUniqueInput
  }

  /**
   * Session deleteMany
   */
  export type SessionDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Sessions to delete
     */
    where?: SessionWhereInput
    /**
     * Limit how many Sessions to delete.
     */
    limit?: number
  }

  /**
   * Session without action
   */
  export type SessionDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Session
     */
    select?: SessionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Session
     */
    omit?: SessionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionInclude<ExtArgs> | null
  }


  /**
   * Model Star
   */

  export type AggregateStar = {
    _count: StarCountAggregateOutputType | null
    _avg: StarAvgAggregateOutputType | null
    _sum: StarSumAggregateOutputType | null
    _min: StarMinAggregateOutputType | null
    _max: StarMaxAggregateOutputType | null
  }

  export type StarAvgAggregateOutputType = {
    brightness: number | null
    size: number | null
  }

  export type StarSumAggregateOutputType = {
    brightness: number | null
    size: number | null
  }

  export type StarMinAggregateOutputType = {
    id: string | null
    userId: string | null
    title: string | null
    content: string | null
    type: $Enums.StarType | null
    mediaUrl: string | null
    emotion: $Enums.Emotion | null
    brightness: number | null
    isSealed: boolean | null
    sealedUntil: Date | null
    color: string | null
    size: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StarMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    title: string | null
    content: string | null
    type: $Enums.StarType | null
    mediaUrl: string | null
    emotion: $Enums.Emotion | null
    brightness: number | null
    isSealed: boolean | null
    sealedUntil: Date | null
    color: string | null
    size: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StarCountAggregateOutputType = {
    id: number
    userId: number
    title: number
    content: number
    type: number
    mediaUrl: number
    emotion: number
    brightness: number
    isSealed: number
    sealedUntil: number
    position: number
    color: number
    size: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type StarAvgAggregateInputType = {
    brightness?: true
    size?: true
  }

  export type StarSumAggregateInputType = {
    brightness?: true
    size?: true
  }

  export type StarMinAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    content?: true
    type?: true
    mediaUrl?: true
    emotion?: true
    brightness?: true
    isSealed?: true
    sealedUntil?: true
    color?: true
    size?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StarMaxAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    content?: true
    type?: true
    mediaUrl?: true
    emotion?: true
    brightness?: true
    isSealed?: true
    sealedUntil?: true
    color?: true
    size?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StarCountAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    content?: true
    type?: true
    mediaUrl?: true
    emotion?: true
    brightness?: true
    isSealed?: true
    sealedUntil?: true
    position?: true
    color?: true
    size?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type StarAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Star to aggregate.
     */
    where?: StarWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Stars to fetch.
     */
    orderBy?: StarOrderByWithRelationInput | StarOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: StarWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Stars from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Stars.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Stars
    **/
    _count?: true | StarCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: StarAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: StarSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: StarMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: StarMaxAggregateInputType
  }

  export type GetStarAggregateType<T extends StarAggregateArgs> = {
        [P in keyof T & keyof AggregateStar]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateStar[P]>
      : GetScalarType<T[P], AggregateStar[P]>
  }




  export type StarGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StarWhereInput
    orderBy?: StarOrderByWithAggregationInput | StarOrderByWithAggregationInput[]
    by: StarScalarFieldEnum[] | StarScalarFieldEnum
    having?: StarScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: StarCountAggregateInputType | true
    _avg?: StarAvgAggregateInputType
    _sum?: StarSumAggregateInputType
    _min?: StarMinAggregateInputType
    _max?: StarMaxAggregateInputType
  }

  export type StarGroupByOutputType = {
    id: string
    userId: string
    title: string | null
    content: string
    type: $Enums.StarType
    mediaUrl: string | null
    emotion: $Enums.Emotion
    brightness: number
    isSealed: boolean
    sealedUntil: Date | null
    position: JsonValue | null
    color: string | null
    size: number
    createdAt: Date
    updatedAt: Date
    _count: StarCountAggregateOutputType | null
    _avg: StarAvgAggregateOutputType | null
    _sum: StarSumAggregateOutputType | null
    _min: StarMinAggregateOutputType | null
    _max: StarMaxAggregateOutputType | null
  }

  type GetStarGroupByPayload<T extends StarGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<StarGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof StarGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], StarGroupByOutputType[P]>
            : GetScalarType<T[P], StarGroupByOutputType[P]>
        }
      >
    >


  export type StarSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    content?: boolean
    type?: boolean
    mediaUrl?: boolean
    emotion?: boolean
    brightness?: boolean
    isSealed?: boolean
    sealedUntil?: boolean
    position?: boolean
    color?: boolean
    size?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    resonances?: boolean | Star$resonancesArgs<ExtArgs>
    _count?: boolean | StarCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["star"]>

  export type StarSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    content?: boolean
    type?: boolean
    mediaUrl?: boolean
    emotion?: boolean
    brightness?: boolean
    isSealed?: boolean
    sealedUntil?: boolean
    position?: boolean
    color?: boolean
    size?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["star"]>

  export type StarSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    content?: boolean
    type?: boolean
    mediaUrl?: boolean
    emotion?: boolean
    brightness?: boolean
    isSealed?: boolean
    sealedUntil?: boolean
    position?: boolean
    color?: boolean
    size?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["star"]>

  export type StarSelectScalar = {
    id?: boolean
    userId?: boolean
    title?: boolean
    content?: boolean
    type?: boolean
    mediaUrl?: boolean
    emotion?: boolean
    brightness?: boolean
    isSealed?: boolean
    sealedUntil?: boolean
    position?: boolean
    color?: boolean
    size?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type StarOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "title" | "content" | "type" | "mediaUrl" | "emotion" | "brightness" | "isSealed" | "sealedUntil" | "position" | "color" | "size" | "createdAt" | "updatedAt", ExtArgs["result"]["star"]>
  export type StarInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    resonances?: boolean | Star$resonancesArgs<ExtArgs>
    _count?: boolean | StarCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type StarIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type StarIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $StarPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Star"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      resonances: Prisma.$ResonancePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      title: string | null
      content: string
      type: $Enums.StarType
      mediaUrl: string | null
      emotion: $Enums.Emotion
      brightness: number
      isSealed: boolean
      sealedUntil: Date | null
      position: Prisma.JsonValue | null
      color: string | null
      size: number
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["star"]>
    composites: {}
  }

  type StarGetPayload<S extends boolean | null | undefined | StarDefaultArgs> = $Result.GetResult<Prisma.$StarPayload, S>

  type StarCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<StarFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: StarCountAggregateInputType | true
    }

  export interface StarDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Star'], meta: { name: 'Star' } }
    /**
     * Find zero or one Star that matches the filter.
     * @param {StarFindUniqueArgs} args - Arguments to find a Star
     * @example
     * // Get one Star
     * const star = await prisma.star.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends StarFindUniqueArgs>(args: SelectSubset<T, StarFindUniqueArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Star that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {StarFindUniqueOrThrowArgs} args - Arguments to find a Star
     * @example
     * // Get one Star
     * const star = await prisma.star.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends StarFindUniqueOrThrowArgs>(args: SelectSubset<T, StarFindUniqueOrThrowArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Star that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StarFindFirstArgs} args - Arguments to find a Star
     * @example
     * // Get one Star
     * const star = await prisma.star.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends StarFindFirstArgs>(args?: SelectSubset<T, StarFindFirstArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Star that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StarFindFirstOrThrowArgs} args - Arguments to find a Star
     * @example
     * // Get one Star
     * const star = await prisma.star.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends StarFindFirstOrThrowArgs>(args?: SelectSubset<T, StarFindFirstOrThrowArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Stars that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StarFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Stars
     * const stars = await prisma.star.findMany()
     * 
     * // Get first 10 Stars
     * const stars = await prisma.star.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const starWithIdOnly = await prisma.star.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends StarFindManyArgs>(args?: SelectSubset<T, StarFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Star.
     * @param {StarCreateArgs} args - Arguments to create a Star.
     * @example
     * // Create one Star
     * const Star = await prisma.star.create({
     *   data: {
     *     // ... data to create a Star
     *   }
     * })
     * 
     */
    create<T extends StarCreateArgs>(args: SelectSubset<T, StarCreateArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Stars.
     * @param {StarCreateManyArgs} args - Arguments to create many Stars.
     * @example
     * // Create many Stars
     * const star = await prisma.star.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends StarCreateManyArgs>(args?: SelectSubset<T, StarCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Stars and returns the data saved in the database.
     * @param {StarCreateManyAndReturnArgs} args - Arguments to create many Stars.
     * @example
     * // Create many Stars
     * const star = await prisma.star.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Stars and only return the `id`
     * const starWithIdOnly = await prisma.star.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends StarCreateManyAndReturnArgs>(args?: SelectSubset<T, StarCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Star.
     * @param {StarDeleteArgs} args - Arguments to delete one Star.
     * @example
     * // Delete one Star
     * const Star = await prisma.star.delete({
     *   where: {
     *     // ... filter to delete one Star
     *   }
     * })
     * 
     */
    delete<T extends StarDeleteArgs>(args: SelectSubset<T, StarDeleteArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Star.
     * @param {StarUpdateArgs} args - Arguments to update one Star.
     * @example
     * // Update one Star
     * const star = await prisma.star.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends StarUpdateArgs>(args: SelectSubset<T, StarUpdateArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Stars.
     * @param {StarDeleteManyArgs} args - Arguments to filter Stars to delete.
     * @example
     * // Delete a few Stars
     * const { count } = await prisma.star.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends StarDeleteManyArgs>(args?: SelectSubset<T, StarDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Stars.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StarUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Stars
     * const star = await prisma.star.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends StarUpdateManyArgs>(args: SelectSubset<T, StarUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Stars and returns the data updated in the database.
     * @param {StarUpdateManyAndReturnArgs} args - Arguments to update many Stars.
     * @example
     * // Update many Stars
     * const star = await prisma.star.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Stars and only return the `id`
     * const starWithIdOnly = await prisma.star.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends StarUpdateManyAndReturnArgs>(args: SelectSubset<T, StarUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Star.
     * @param {StarUpsertArgs} args - Arguments to update or create a Star.
     * @example
     * // Update or create a Star
     * const star = await prisma.star.upsert({
     *   create: {
     *     // ... data to create a Star
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Star we want to update
     *   }
     * })
     */
    upsert<T extends StarUpsertArgs>(args: SelectSubset<T, StarUpsertArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Stars.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StarCountArgs} args - Arguments to filter Stars to count.
     * @example
     * // Count the number of Stars
     * const count = await prisma.star.count({
     *   where: {
     *     // ... the filter for the Stars we want to count
     *   }
     * })
    **/
    count<T extends StarCountArgs>(
      args?: Subset<T, StarCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], StarCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Star.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StarAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends StarAggregateArgs>(args: Subset<T, StarAggregateArgs>): Prisma.PrismaPromise<GetStarAggregateType<T>>

    /**
     * Group by Star.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StarGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends StarGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: StarGroupByArgs['orderBy'] }
        : { orderBy?: StarGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, StarGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStarGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Star model
   */
  readonly fields: StarFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Star.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__StarClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    resonances<T extends Star$resonancesArgs<ExtArgs> = {}>(args?: Subset<T, Star$resonancesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Star model
   */
  interface StarFieldRefs {
    readonly id: FieldRef<"Star", 'String'>
    readonly userId: FieldRef<"Star", 'String'>
    readonly title: FieldRef<"Star", 'String'>
    readonly content: FieldRef<"Star", 'String'>
    readonly type: FieldRef<"Star", 'StarType'>
    readonly mediaUrl: FieldRef<"Star", 'String'>
    readonly emotion: FieldRef<"Star", 'Emotion'>
    readonly brightness: FieldRef<"Star", 'Float'>
    readonly isSealed: FieldRef<"Star", 'Boolean'>
    readonly sealedUntil: FieldRef<"Star", 'DateTime'>
    readonly position: FieldRef<"Star", 'Json'>
    readonly color: FieldRef<"Star", 'String'>
    readonly size: FieldRef<"Star", 'Float'>
    readonly createdAt: FieldRef<"Star", 'DateTime'>
    readonly updatedAt: FieldRef<"Star", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Star findUnique
   */
  export type StarFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * Filter, which Star to fetch.
     */
    where: StarWhereUniqueInput
  }

  /**
   * Star findUniqueOrThrow
   */
  export type StarFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * Filter, which Star to fetch.
     */
    where: StarWhereUniqueInput
  }

  /**
   * Star findFirst
   */
  export type StarFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * Filter, which Star to fetch.
     */
    where?: StarWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Stars to fetch.
     */
    orderBy?: StarOrderByWithRelationInput | StarOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Stars.
     */
    cursor?: StarWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Stars from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Stars.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Stars.
     */
    distinct?: StarScalarFieldEnum | StarScalarFieldEnum[]
  }

  /**
   * Star findFirstOrThrow
   */
  export type StarFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * Filter, which Star to fetch.
     */
    where?: StarWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Stars to fetch.
     */
    orderBy?: StarOrderByWithRelationInput | StarOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Stars.
     */
    cursor?: StarWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Stars from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Stars.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Stars.
     */
    distinct?: StarScalarFieldEnum | StarScalarFieldEnum[]
  }

  /**
   * Star findMany
   */
  export type StarFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * Filter, which Stars to fetch.
     */
    where?: StarWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Stars to fetch.
     */
    orderBy?: StarOrderByWithRelationInput | StarOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Stars.
     */
    cursor?: StarWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Stars from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Stars.
     */
    skip?: number
    distinct?: StarScalarFieldEnum | StarScalarFieldEnum[]
  }

  /**
   * Star create
   */
  export type StarCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * The data needed to create a Star.
     */
    data: XOR<StarCreateInput, StarUncheckedCreateInput>
  }

  /**
   * Star createMany
   */
  export type StarCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Stars.
     */
    data: StarCreateManyInput | StarCreateManyInput[]
  }

  /**
   * Star createManyAndReturn
   */
  export type StarCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * The data used to create many Stars.
     */
    data: StarCreateManyInput | StarCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Star update
   */
  export type StarUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * The data needed to update a Star.
     */
    data: XOR<StarUpdateInput, StarUncheckedUpdateInput>
    /**
     * Choose, which Star to update.
     */
    where: StarWhereUniqueInput
  }

  /**
   * Star updateMany
   */
  export type StarUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Stars.
     */
    data: XOR<StarUpdateManyMutationInput, StarUncheckedUpdateManyInput>
    /**
     * Filter which Stars to update
     */
    where?: StarWhereInput
    /**
     * Limit how many Stars to update.
     */
    limit?: number
  }

  /**
   * Star updateManyAndReturn
   */
  export type StarUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * The data used to update Stars.
     */
    data: XOR<StarUpdateManyMutationInput, StarUncheckedUpdateManyInput>
    /**
     * Filter which Stars to update
     */
    where?: StarWhereInput
    /**
     * Limit how many Stars to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Star upsert
   */
  export type StarUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * The filter to search for the Star to update in case it exists.
     */
    where: StarWhereUniqueInput
    /**
     * In case the Star found by the `where` argument doesn't exist, create a new Star with this data.
     */
    create: XOR<StarCreateInput, StarUncheckedCreateInput>
    /**
     * In case the Star was found with the provided `where` argument, update it with this data.
     */
    update: XOR<StarUpdateInput, StarUncheckedUpdateInput>
  }

  /**
   * Star delete
   */
  export type StarDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
    /**
     * Filter which Star to delete.
     */
    where: StarWhereUniqueInput
  }

  /**
   * Star deleteMany
   */
  export type StarDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Stars to delete
     */
    where?: StarWhereInput
    /**
     * Limit how many Stars to delete.
     */
    limit?: number
  }

  /**
   * Star.resonances
   */
  export type Star$resonancesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    where?: ResonanceWhereInput
    orderBy?: ResonanceOrderByWithRelationInput | ResonanceOrderByWithRelationInput[]
    cursor?: ResonanceWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ResonanceScalarFieldEnum | ResonanceScalarFieldEnum[]
  }

  /**
   * Star without action
   */
  export type StarDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Star
     */
    select?: StarSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Star
     */
    omit?: StarOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StarInclude<ExtArgs> | null
  }


  /**
   * Model Resonance
   */

  export type AggregateResonance = {
    _count: ResonanceCountAggregateOutputType | null
    _min: ResonanceMinAggregateOutputType | null
    _max: ResonanceMaxAggregateOutputType | null
  }

  export type ResonanceMinAggregateOutputType = {
    id: string | null
    starId: string | null
    userId: string | null
    type: $Enums.ResonanceType | null
    message: string | null
    isAnonymous: boolean | null
    createdAt: Date | null
  }

  export type ResonanceMaxAggregateOutputType = {
    id: string | null
    starId: string | null
    userId: string | null
    type: $Enums.ResonanceType | null
    message: string | null
    isAnonymous: boolean | null
    createdAt: Date | null
  }

  export type ResonanceCountAggregateOutputType = {
    id: number
    starId: number
    userId: number
    type: number
    message: number
    isAnonymous: number
    createdAt: number
    _all: number
  }


  export type ResonanceMinAggregateInputType = {
    id?: true
    starId?: true
    userId?: true
    type?: true
    message?: true
    isAnonymous?: true
    createdAt?: true
  }

  export type ResonanceMaxAggregateInputType = {
    id?: true
    starId?: true
    userId?: true
    type?: true
    message?: true
    isAnonymous?: true
    createdAt?: true
  }

  export type ResonanceCountAggregateInputType = {
    id?: true
    starId?: true
    userId?: true
    type?: true
    message?: true
    isAnonymous?: true
    createdAt?: true
    _all?: true
  }

  export type ResonanceAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Resonance to aggregate.
     */
    where?: ResonanceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Resonances to fetch.
     */
    orderBy?: ResonanceOrderByWithRelationInput | ResonanceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ResonanceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Resonances from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Resonances.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Resonances
    **/
    _count?: true | ResonanceCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ResonanceMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ResonanceMaxAggregateInputType
  }

  export type GetResonanceAggregateType<T extends ResonanceAggregateArgs> = {
        [P in keyof T & keyof AggregateResonance]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateResonance[P]>
      : GetScalarType<T[P], AggregateResonance[P]>
  }




  export type ResonanceGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ResonanceWhereInput
    orderBy?: ResonanceOrderByWithAggregationInput | ResonanceOrderByWithAggregationInput[]
    by: ResonanceScalarFieldEnum[] | ResonanceScalarFieldEnum
    having?: ResonanceScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ResonanceCountAggregateInputType | true
    _min?: ResonanceMinAggregateInputType
    _max?: ResonanceMaxAggregateInputType
  }

  export type ResonanceGroupByOutputType = {
    id: string
    starId: string
    userId: string
    type: $Enums.ResonanceType
    message: string | null
    isAnonymous: boolean
    createdAt: Date
    _count: ResonanceCountAggregateOutputType | null
    _min: ResonanceMinAggregateOutputType | null
    _max: ResonanceMaxAggregateOutputType | null
  }

  type GetResonanceGroupByPayload<T extends ResonanceGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ResonanceGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ResonanceGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ResonanceGroupByOutputType[P]>
            : GetScalarType<T[P], ResonanceGroupByOutputType[P]>
        }
      >
    >


  export type ResonanceSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    starId?: boolean
    userId?: boolean
    type?: boolean
    message?: boolean
    isAnonymous?: boolean
    createdAt?: boolean
    star?: boolean | StarDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["resonance"]>

  export type ResonanceSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    starId?: boolean
    userId?: boolean
    type?: boolean
    message?: boolean
    isAnonymous?: boolean
    createdAt?: boolean
    star?: boolean | StarDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["resonance"]>

  export type ResonanceSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    starId?: boolean
    userId?: boolean
    type?: boolean
    message?: boolean
    isAnonymous?: boolean
    createdAt?: boolean
    star?: boolean | StarDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["resonance"]>

  export type ResonanceSelectScalar = {
    id?: boolean
    starId?: boolean
    userId?: boolean
    type?: boolean
    message?: boolean
    isAnonymous?: boolean
    createdAt?: boolean
  }

  export type ResonanceOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "starId" | "userId" | "type" | "message" | "isAnonymous" | "createdAt", ExtArgs["result"]["resonance"]>
  export type ResonanceInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    star?: boolean | StarDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type ResonanceIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    star?: boolean | StarDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type ResonanceIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    star?: boolean | StarDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $ResonancePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Resonance"
    objects: {
      star: Prisma.$StarPayload<ExtArgs>
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      starId: string
      userId: string
      type: $Enums.ResonanceType
      message: string | null
      isAnonymous: boolean
      createdAt: Date
    }, ExtArgs["result"]["resonance"]>
    composites: {}
  }

  type ResonanceGetPayload<S extends boolean | null | undefined | ResonanceDefaultArgs> = $Result.GetResult<Prisma.$ResonancePayload, S>

  type ResonanceCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ResonanceFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ResonanceCountAggregateInputType | true
    }

  export interface ResonanceDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Resonance'], meta: { name: 'Resonance' } }
    /**
     * Find zero or one Resonance that matches the filter.
     * @param {ResonanceFindUniqueArgs} args - Arguments to find a Resonance
     * @example
     * // Get one Resonance
     * const resonance = await prisma.resonance.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ResonanceFindUniqueArgs>(args: SelectSubset<T, ResonanceFindUniqueArgs<ExtArgs>>): Prisma__ResonanceClient<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Resonance that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ResonanceFindUniqueOrThrowArgs} args - Arguments to find a Resonance
     * @example
     * // Get one Resonance
     * const resonance = await prisma.resonance.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ResonanceFindUniqueOrThrowArgs>(args: SelectSubset<T, ResonanceFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ResonanceClient<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Resonance that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ResonanceFindFirstArgs} args - Arguments to find a Resonance
     * @example
     * // Get one Resonance
     * const resonance = await prisma.resonance.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ResonanceFindFirstArgs>(args?: SelectSubset<T, ResonanceFindFirstArgs<ExtArgs>>): Prisma__ResonanceClient<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Resonance that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ResonanceFindFirstOrThrowArgs} args - Arguments to find a Resonance
     * @example
     * // Get one Resonance
     * const resonance = await prisma.resonance.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ResonanceFindFirstOrThrowArgs>(args?: SelectSubset<T, ResonanceFindFirstOrThrowArgs<ExtArgs>>): Prisma__ResonanceClient<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Resonances that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ResonanceFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Resonances
     * const resonances = await prisma.resonance.findMany()
     * 
     * // Get first 10 Resonances
     * const resonances = await prisma.resonance.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const resonanceWithIdOnly = await prisma.resonance.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ResonanceFindManyArgs>(args?: SelectSubset<T, ResonanceFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Resonance.
     * @param {ResonanceCreateArgs} args - Arguments to create a Resonance.
     * @example
     * // Create one Resonance
     * const Resonance = await prisma.resonance.create({
     *   data: {
     *     // ... data to create a Resonance
     *   }
     * })
     * 
     */
    create<T extends ResonanceCreateArgs>(args: SelectSubset<T, ResonanceCreateArgs<ExtArgs>>): Prisma__ResonanceClient<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Resonances.
     * @param {ResonanceCreateManyArgs} args - Arguments to create many Resonances.
     * @example
     * // Create many Resonances
     * const resonance = await prisma.resonance.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ResonanceCreateManyArgs>(args?: SelectSubset<T, ResonanceCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Resonances and returns the data saved in the database.
     * @param {ResonanceCreateManyAndReturnArgs} args - Arguments to create many Resonances.
     * @example
     * // Create many Resonances
     * const resonance = await prisma.resonance.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Resonances and only return the `id`
     * const resonanceWithIdOnly = await prisma.resonance.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ResonanceCreateManyAndReturnArgs>(args?: SelectSubset<T, ResonanceCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Resonance.
     * @param {ResonanceDeleteArgs} args - Arguments to delete one Resonance.
     * @example
     * // Delete one Resonance
     * const Resonance = await prisma.resonance.delete({
     *   where: {
     *     // ... filter to delete one Resonance
     *   }
     * })
     * 
     */
    delete<T extends ResonanceDeleteArgs>(args: SelectSubset<T, ResonanceDeleteArgs<ExtArgs>>): Prisma__ResonanceClient<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Resonance.
     * @param {ResonanceUpdateArgs} args - Arguments to update one Resonance.
     * @example
     * // Update one Resonance
     * const resonance = await prisma.resonance.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ResonanceUpdateArgs>(args: SelectSubset<T, ResonanceUpdateArgs<ExtArgs>>): Prisma__ResonanceClient<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Resonances.
     * @param {ResonanceDeleteManyArgs} args - Arguments to filter Resonances to delete.
     * @example
     * // Delete a few Resonances
     * const { count } = await prisma.resonance.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ResonanceDeleteManyArgs>(args?: SelectSubset<T, ResonanceDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Resonances.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ResonanceUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Resonances
     * const resonance = await prisma.resonance.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ResonanceUpdateManyArgs>(args: SelectSubset<T, ResonanceUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Resonances and returns the data updated in the database.
     * @param {ResonanceUpdateManyAndReturnArgs} args - Arguments to update many Resonances.
     * @example
     * // Update many Resonances
     * const resonance = await prisma.resonance.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Resonances and only return the `id`
     * const resonanceWithIdOnly = await prisma.resonance.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ResonanceUpdateManyAndReturnArgs>(args: SelectSubset<T, ResonanceUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Resonance.
     * @param {ResonanceUpsertArgs} args - Arguments to update or create a Resonance.
     * @example
     * // Update or create a Resonance
     * const resonance = await prisma.resonance.upsert({
     *   create: {
     *     // ... data to create a Resonance
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Resonance we want to update
     *   }
     * })
     */
    upsert<T extends ResonanceUpsertArgs>(args: SelectSubset<T, ResonanceUpsertArgs<ExtArgs>>): Prisma__ResonanceClient<$Result.GetResult<Prisma.$ResonancePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Resonances.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ResonanceCountArgs} args - Arguments to filter Resonances to count.
     * @example
     * // Count the number of Resonances
     * const count = await prisma.resonance.count({
     *   where: {
     *     // ... the filter for the Resonances we want to count
     *   }
     * })
    **/
    count<T extends ResonanceCountArgs>(
      args?: Subset<T, ResonanceCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ResonanceCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Resonance.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ResonanceAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ResonanceAggregateArgs>(args: Subset<T, ResonanceAggregateArgs>): Prisma.PrismaPromise<GetResonanceAggregateType<T>>

    /**
     * Group by Resonance.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ResonanceGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ResonanceGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ResonanceGroupByArgs['orderBy'] }
        : { orderBy?: ResonanceGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ResonanceGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetResonanceGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Resonance model
   */
  readonly fields: ResonanceFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Resonance.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ResonanceClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    star<T extends StarDefaultArgs<ExtArgs> = {}>(args?: Subset<T, StarDefaultArgs<ExtArgs>>): Prisma__StarClient<$Result.GetResult<Prisma.$StarPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Resonance model
   */
  interface ResonanceFieldRefs {
    readonly id: FieldRef<"Resonance", 'String'>
    readonly starId: FieldRef<"Resonance", 'String'>
    readonly userId: FieldRef<"Resonance", 'String'>
    readonly type: FieldRef<"Resonance", 'ResonanceType'>
    readonly message: FieldRef<"Resonance", 'String'>
    readonly isAnonymous: FieldRef<"Resonance", 'Boolean'>
    readonly createdAt: FieldRef<"Resonance", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Resonance findUnique
   */
  export type ResonanceFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * Filter, which Resonance to fetch.
     */
    where: ResonanceWhereUniqueInput
  }

  /**
   * Resonance findUniqueOrThrow
   */
  export type ResonanceFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * Filter, which Resonance to fetch.
     */
    where: ResonanceWhereUniqueInput
  }

  /**
   * Resonance findFirst
   */
  export type ResonanceFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * Filter, which Resonance to fetch.
     */
    where?: ResonanceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Resonances to fetch.
     */
    orderBy?: ResonanceOrderByWithRelationInput | ResonanceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Resonances.
     */
    cursor?: ResonanceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Resonances from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Resonances.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Resonances.
     */
    distinct?: ResonanceScalarFieldEnum | ResonanceScalarFieldEnum[]
  }

  /**
   * Resonance findFirstOrThrow
   */
  export type ResonanceFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * Filter, which Resonance to fetch.
     */
    where?: ResonanceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Resonances to fetch.
     */
    orderBy?: ResonanceOrderByWithRelationInput | ResonanceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Resonances.
     */
    cursor?: ResonanceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Resonances from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Resonances.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Resonances.
     */
    distinct?: ResonanceScalarFieldEnum | ResonanceScalarFieldEnum[]
  }

  /**
   * Resonance findMany
   */
  export type ResonanceFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * Filter, which Resonances to fetch.
     */
    where?: ResonanceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Resonances to fetch.
     */
    orderBy?: ResonanceOrderByWithRelationInput | ResonanceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Resonances.
     */
    cursor?: ResonanceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Resonances from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Resonances.
     */
    skip?: number
    distinct?: ResonanceScalarFieldEnum | ResonanceScalarFieldEnum[]
  }

  /**
   * Resonance create
   */
  export type ResonanceCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * The data needed to create a Resonance.
     */
    data: XOR<ResonanceCreateInput, ResonanceUncheckedCreateInput>
  }

  /**
   * Resonance createMany
   */
  export type ResonanceCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Resonances.
     */
    data: ResonanceCreateManyInput | ResonanceCreateManyInput[]
  }

  /**
   * Resonance createManyAndReturn
   */
  export type ResonanceCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * The data used to create many Resonances.
     */
    data: ResonanceCreateManyInput | ResonanceCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Resonance update
   */
  export type ResonanceUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * The data needed to update a Resonance.
     */
    data: XOR<ResonanceUpdateInput, ResonanceUncheckedUpdateInput>
    /**
     * Choose, which Resonance to update.
     */
    where: ResonanceWhereUniqueInput
  }

  /**
   * Resonance updateMany
   */
  export type ResonanceUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Resonances.
     */
    data: XOR<ResonanceUpdateManyMutationInput, ResonanceUncheckedUpdateManyInput>
    /**
     * Filter which Resonances to update
     */
    where?: ResonanceWhereInput
    /**
     * Limit how many Resonances to update.
     */
    limit?: number
  }

  /**
   * Resonance updateManyAndReturn
   */
  export type ResonanceUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * The data used to update Resonances.
     */
    data: XOR<ResonanceUpdateManyMutationInput, ResonanceUncheckedUpdateManyInput>
    /**
     * Filter which Resonances to update
     */
    where?: ResonanceWhereInput
    /**
     * Limit how many Resonances to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Resonance upsert
   */
  export type ResonanceUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * The filter to search for the Resonance to update in case it exists.
     */
    where: ResonanceWhereUniqueInput
    /**
     * In case the Resonance found by the `where` argument doesn't exist, create a new Resonance with this data.
     */
    create: XOR<ResonanceCreateInput, ResonanceUncheckedCreateInput>
    /**
     * In case the Resonance was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ResonanceUpdateInput, ResonanceUncheckedUpdateInput>
  }

  /**
   * Resonance delete
   */
  export type ResonanceDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
    /**
     * Filter which Resonance to delete.
     */
    where: ResonanceWhereUniqueInput
  }

  /**
   * Resonance deleteMany
   */
  export type ResonanceDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Resonances to delete
     */
    where?: ResonanceWhereInput
    /**
     * Limit how many Resonances to delete.
     */
    limit?: number
  }

  /**
   * Resonance without action
   */
  export type ResonanceDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Resonance
     */
    select?: ResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Resonance
     */
    omit?: ResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ResonanceInclude<ExtArgs> | null
  }


  /**
   * Model GalaxyEmotion
   */

  export type AggregateGalaxyEmotion = {
    _count: GalaxyEmotionCountAggregateOutputType | null
    _avg: GalaxyEmotionAvgAggregateOutputType | null
    _sum: GalaxyEmotionSumAggregateOutputType | null
    _min: GalaxyEmotionMinAggregateOutputType | null
    _max: GalaxyEmotionMaxAggregateOutputType | null
  }

  export type GalaxyEmotionAvgAggregateOutputType = {
    intensity: number | null
    size: number | null
  }

  export type GalaxyEmotionSumAggregateOutputType = {
    intensity: number | null
    size: number | null
  }

  export type GalaxyEmotionMinAggregateOutputType = {
    id: string | null
    content: string | null
    emotion: $Enums.Emotion | null
    intensity: number | null
    color: string | null
    size: number | null
    expiresAt: Date | null
    createdAt: Date | null
  }

  export type GalaxyEmotionMaxAggregateOutputType = {
    id: string | null
    content: string | null
    emotion: $Enums.Emotion | null
    intensity: number | null
    color: string | null
    size: number | null
    expiresAt: Date | null
    createdAt: Date | null
  }

  export type GalaxyEmotionCountAggregateOutputType = {
    id: number
    content: number
    emotion: number
    intensity: number
    position: number
    color: number
    size: number
    expiresAt: number
    createdAt: number
    _all: number
  }


  export type GalaxyEmotionAvgAggregateInputType = {
    intensity?: true
    size?: true
  }

  export type GalaxyEmotionSumAggregateInputType = {
    intensity?: true
    size?: true
  }

  export type GalaxyEmotionMinAggregateInputType = {
    id?: true
    content?: true
    emotion?: true
    intensity?: true
    color?: true
    size?: true
    expiresAt?: true
    createdAt?: true
  }

  export type GalaxyEmotionMaxAggregateInputType = {
    id?: true
    content?: true
    emotion?: true
    intensity?: true
    color?: true
    size?: true
    expiresAt?: true
    createdAt?: true
  }

  export type GalaxyEmotionCountAggregateInputType = {
    id?: true
    content?: true
    emotion?: true
    intensity?: true
    position?: true
    color?: true
    size?: true
    expiresAt?: true
    createdAt?: true
    _all?: true
  }

  export type GalaxyEmotionAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which GalaxyEmotion to aggregate.
     */
    where?: GalaxyEmotionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GalaxyEmotions to fetch.
     */
    orderBy?: GalaxyEmotionOrderByWithRelationInput | GalaxyEmotionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: GalaxyEmotionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GalaxyEmotions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GalaxyEmotions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned GalaxyEmotions
    **/
    _count?: true | GalaxyEmotionCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: GalaxyEmotionAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: GalaxyEmotionSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: GalaxyEmotionMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: GalaxyEmotionMaxAggregateInputType
  }

  export type GetGalaxyEmotionAggregateType<T extends GalaxyEmotionAggregateArgs> = {
        [P in keyof T & keyof AggregateGalaxyEmotion]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateGalaxyEmotion[P]>
      : GetScalarType<T[P], AggregateGalaxyEmotion[P]>
  }




  export type GalaxyEmotionGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GalaxyEmotionWhereInput
    orderBy?: GalaxyEmotionOrderByWithAggregationInput | GalaxyEmotionOrderByWithAggregationInput[]
    by: GalaxyEmotionScalarFieldEnum[] | GalaxyEmotionScalarFieldEnum
    having?: GalaxyEmotionScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: GalaxyEmotionCountAggregateInputType | true
    _avg?: GalaxyEmotionAvgAggregateInputType
    _sum?: GalaxyEmotionSumAggregateInputType
    _min?: GalaxyEmotionMinAggregateInputType
    _max?: GalaxyEmotionMaxAggregateInputType
  }

  export type GalaxyEmotionGroupByOutputType = {
    id: string
    content: string
    emotion: $Enums.Emotion
    intensity: number
    position: JsonValue
    color: string
    size: number
    expiresAt: Date
    createdAt: Date
    _count: GalaxyEmotionCountAggregateOutputType | null
    _avg: GalaxyEmotionAvgAggregateOutputType | null
    _sum: GalaxyEmotionSumAggregateOutputType | null
    _min: GalaxyEmotionMinAggregateOutputType | null
    _max: GalaxyEmotionMaxAggregateOutputType | null
  }

  type GetGalaxyEmotionGroupByPayload<T extends GalaxyEmotionGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<GalaxyEmotionGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof GalaxyEmotionGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], GalaxyEmotionGroupByOutputType[P]>
            : GetScalarType<T[P], GalaxyEmotionGroupByOutputType[P]>
        }
      >
    >


  export type GalaxyEmotionSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    content?: boolean
    emotion?: boolean
    intensity?: boolean
    position?: boolean
    color?: boolean
    size?: boolean
    expiresAt?: boolean
    createdAt?: boolean
    resonances?: boolean | GalaxyEmotion$resonancesArgs<ExtArgs>
    _count?: boolean | GalaxyEmotionCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["galaxyEmotion"]>

  export type GalaxyEmotionSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    content?: boolean
    emotion?: boolean
    intensity?: boolean
    position?: boolean
    color?: boolean
    size?: boolean
    expiresAt?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["galaxyEmotion"]>

  export type GalaxyEmotionSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    content?: boolean
    emotion?: boolean
    intensity?: boolean
    position?: boolean
    color?: boolean
    size?: boolean
    expiresAt?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["galaxyEmotion"]>

  export type GalaxyEmotionSelectScalar = {
    id?: boolean
    content?: boolean
    emotion?: boolean
    intensity?: boolean
    position?: boolean
    color?: boolean
    size?: boolean
    expiresAt?: boolean
    createdAt?: boolean
  }

  export type GalaxyEmotionOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "content" | "emotion" | "intensity" | "position" | "color" | "size" | "expiresAt" | "createdAt", ExtArgs["result"]["galaxyEmotion"]>
  export type GalaxyEmotionInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    resonances?: boolean | GalaxyEmotion$resonancesArgs<ExtArgs>
    _count?: boolean | GalaxyEmotionCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type GalaxyEmotionIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type GalaxyEmotionIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $GalaxyEmotionPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "GalaxyEmotion"
    objects: {
      resonances: Prisma.$GalaxyResonancePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      content: string
      emotion: $Enums.Emotion
      intensity: number
      position: Prisma.JsonValue
      color: string
      size: number
      expiresAt: Date
      createdAt: Date
    }, ExtArgs["result"]["galaxyEmotion"]>
    composites: {}
  }

  type GalaxyEmotionGetPayload<S extends boolean | null | undefined | GalaxyEmotionDefaultArgs> = $Result.GetResult<Prisma.$GalaxyEmotionPayload, S>

  type GalaxyEmotionCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<GalaxyEmotionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: GalaxyEmotionCountAggregateInputType | true
    }

  export interface GalaxyEmotionDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['GalaxyEmotion'], meta: { name: 'GalaxyEmotion' } }
    /**
     * Find zero or one GalaxyEmotion that matches the filter.
     * @param {GalaxyEmotionFindUniqueArgs} args - Arguments to find a GalaxyEmotion
     * @example
     * // Get one GalaxyEmotion
     * const galaxyEmotion = await prisma.galaxyEmotion.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends GalaxyEmotionFindUniqueArgs>(args: SelectSubset<T, GalaxyEmotionFindUniqueArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one GalaxyEmotion that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {GalaxyEmotionFindUniqueOrThrowArgs} args - Arguments to find a GalaxyEmotion
     * @example
     * // Get one GalaxyEmotion
     * const galaxyEmotion = await prisma.galaxyEmotion.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends GalaxyEmotionFindUniqueOrThrowArgs>(args: SelectSubset<T, GalaxyEmotionFindUniqueOrThrowArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first GalaxyEmotion that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyEmotionFindFirstArgs} args - Arguments to find a GalaxyEmotion
     * @example
     * // Get one GalaxyEmotion
     * const galaxyEmotion = await prisma.galaxyEmotion.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends GalaxyEmotionFindFirstArgs>(args?: SelectSubset<T, GalaxyEmotionFindFirstArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first GalaxyEmotion that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyEmotionFindFirstOrThrowArgs} args - Arguments to find a GalaxyEmotion
     * @example
     * // Get one GalaxyEmotion
     * const galaxyEmotion = await prisma.galaxyEmotion.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends GalaxyEmotionFindFirstOrThrowArgs>(args?: SelectSubset<T, GalaxyEmotionFindFirstOrThrowArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more GalaxyEmotions that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyEmotionFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all GalaxyEmotions
     * const galaxyEmotions = await prisma.galaxyEmotion.findMany()
     * 
     * // Get first 10 GalaxyEmotions
     * const galaxyEmotions = await prisma.galaxyEmotion.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const galaxyEmotionWithIdOnly = await prisma.galaxyEmotion.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends GalaxyEmotionFindManyArgs>(args?: SelectSubset<T, GalaxyEmotionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a GalaxyEmotion.
     * @param {GalaxyEmotionCreateArgs} args - Arguments to create a GalaxyEmotion.
     * @example
     * // Create one GalaxyEmotion
     * const GalaxyEmotion = await prisma.galaxyEmotion.create({
     *   data: {
     *     // ... data to create a GalaxyEmotion
     *   }
     * })
     * 
     */
    create<T extends GalaxyEmotionCreateArgs>(args: SelectSubset<T, GalaxyEmotionCreateArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many GalaxyEmotions.
     * @param {GalaxyEmotionCreateManyArgs} args - Arguments to create many GalaxyEmotions.
     * @example
     * // Create many GalaxyEmotions
     * const galaxyEmotion = await prisma.galaxyEmotion.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends GalaxyEmotionCreateManyArgs>(args?: SelectSubset<T, GalaxyEmotionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many GalaxyEmotions and returns the data saved in the database.
     * @param {GalaxyEmotionCreateManyAndReturnArgs} args - Arguments to create many GalaxyEmotions.
     * @example
     * // Create many GalaxyEmotions
     * const galaxyEmotion = await prisma.galaxyEmotion.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many GalaxyEmotions and only return the `id`
     * const galaxyEmotionWithIdOnly = await prisma.galaxyEmotion.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends GalaxyEmotionCreateManyAndReturnArgs>(args?: SelectSubset<T, GalaxyEmotionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a GalaxyEmotion.
     * @param {GalaxyEmotionDeleteArgs} args - Arguments to delete one GalaxyEmotion.
     * @example
     * // Delete one GalaxyEmotion
     * const GalaxyEmotion = await prisma.galaxyEmotion.delete({
     *   where: {
     *     // ... filter to delete one GalaxyEmotion
     *   }
     * })
     * 
     */
    delete<T extends GalaxyEmotionDeleteArgs>(args: SelectSubset<T, GalaxyEmotionDeleteArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one GalaxyEmotion.
     * @param {GalaxyEmotionUpdateArgs} args - Arguments to update one GalaxyEmotion.
     * @example
     * // Update one GalaxyEmotion
     * const galaxyEmotion = await prisma.galaxyEmotion.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends GalaxyEmotionUpdateArgs>(args: SelectSubset<T, GalaxyEmotionUpdateArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more GalaxyEmotions.
     * @param {GalaxyEmotionDeleteManyArgs} args - Arguments to filter GalaxyEmotions to delete.
     * @example
     * // Delete a few GalaxyEmotions
     * const { count } = await prisma.galaxyEmotion.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends GalaxyEmotionDeleteManyArgs>(args?: SelectSubset<T, GalaxyEmotionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more GalaxyEmotions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyEmotionUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many GalaxyEmotions
     * const galaxyEmotion = await prisma.galaxyEmotion.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends GalaxyEmotionUpdateManyArgs>(args: SelectSubset<T, GalaxyEmotionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more GalaxyEmotions and returns the data updated in the database.
     * @param {GalaxyEmotionUpdateManyAndReturnArgs} args - Arguments to update many GalaxyEmotions.
     * @example
     * // Update many GalaxyEmotions
     * const galaxyEmotion = await prisma.galaxyEmotion.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more GalaxyEmotions and only return the `id`
     * const galaxyEmotionWithIdOnly = await prisma.galaxyEmotion.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends GalaxyEmotionUpdateManyAndReturnArgs>(args: SelectSubset<T, GalaxyEmotionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one GalaxyEmotion.
     * @param {GalaxyEmotionUpsertArgs} args - Arguments to update or create a GalaxyEmotion.
     * @example
     * // Update or create a GalaxyEmotion
     * const galaxyEmotion = await prisma.galaxyEmotion.upsert({
     *   create: {
     *     // ... data to create a GalaxyEmotion
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the GalaxyEmotion we want to update
     *   }
     * })
     */
    upsert<T extends GalaxyEmotionUpsertArgs>(args: SelectSubset<T, GalaxyEmotionUpsertArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of GalaxyEmotions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyEmotionCountArgs} args - Arguments to filter GalaxyEmotions to count.
     * @example
     * // Count the number of GalaxyEmotions
     * const count = await prisma.galaxyEmotion.count({
     *   where: {
     *     // ... the filter for the GalaxyEmotions we want to count
     *   }
     * })
    **/
    count<T extends GalaxyEmotionCountArgs>(
      args?: Subset<T, GalaxyEmotionCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], GalaxyEmotionCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a GalaxyEmotion.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyEmotionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends GalaxyEmotionAggregateArgs>(args: Subset<T, GalaxyEmotionAggregateArgs>): Prisma.PrismaPromise<GetGalaxyEmotionAggregateType<T>>

    /**
     * Group by GalaxyEmotion.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyEmotionGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends GalaxyEmotionGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: GalaxyEmotionGroupByArgs['orderBy'] }
        : { orderBy?: GalaxyEmotionGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, GalaxyEmotionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGalaxyEmotionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the GalaxyEmotion model
   */
  readonly fields: GalaxyEmotionFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for GalaxyEmotion.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__GalaxyEmotionClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    resonances<T extends GalaxyEmotion$resonancesArgs<ExtArgs> = {}>(args?: Subset<T, GalaxyEmotion$resonancesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the GalaxyEmotion model
   */
  interface GalaxyEmotionFieldRefs {
    readonly id: FieldRef<"GalaxyEmotion", 'String'>
    readonly content: FieldRef<"GalaxyEmotion", 'String'>
    readonly emotion: FieldRef<"GalaxyEmotion", 'Emotion'>
    readonly intensity: FieldRef<"GalaxyEmotion", 'Float'>
    readonly position: FieldRef<"GalaxyEmotion", 'Json'>
    readonly color: FieldRef<"GalaxyEmotion", 'String'>
    readonly size: FieldRef<"GalaxyEmotion", 'Float'>
    readonly expiresAt: FieldRef<"GalaxyEmotion", 'DateTime'>
    readonly createdAt: FieldRef<"GalaxyEmotion", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * GalaxyEmotion findUnique
   */
  export type GalaxyEmotionFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyEmotion to fetch.
     */
    where: GalaxyEmotionWhereUniqueInput
  }

  /**
   * GalaxyEmotion findUniqueOrThrow
   */
  export type GalaxyEmotionFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyEmotion to fetch.
     */
    where: GalaxyEmotionWhereUniqueInput
  }

  /**
   * GalaxyEmotion findFirst
   */
  export type GalaxyEmotionFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyEmotion to fetch.
     */
    where?: GalaxyEmotionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GalaxyEmotions to fetch.
     */
    orderBy?: GalaxyEmotionOrderByWithRelationInput | GalaxyEmotionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for GalaxyEmotions.
     */
    cursor?: GalaxyEmotionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GalaxyEmotions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GalaxyEmotions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of GalaxyEmotions.
     */
    distinct?: GalaxyEmotionScalarFieldEnum | GalaxyEmotionScalarFieldEnum[]
  }

  /**
   * GalaxyEmotion findFirstOrThrow
   */
  export type GalaxyEmotionFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyEmotion to fetch.
     */
    where?: GalaxyEmotionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GalaxyEmotions to fetch.
     */
    orderBy?: GalaxyEmotionOrderByWithRelationInput | GalaxyEmotionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for GalaxyEmotions.
     */
    cursor?: GalaxyEmotionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GalaxyEmotions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GalaxyEmotions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of GalaxyEmotions.
     */
    distinct?: GalaxyEmotionScalarFieldEnum | GalaxyEmotionScalarFieldEnum[]
  }

  /**
   * GalaxyEmotion findMany
   */
  export type GalaxyEmotionFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyEmotions to fetch.
     */
    where?: GalaxyEmotionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GalaxyEmotions to fetch.
     */
    orderBy?: GalaxyEmotionOrderByWithRelationInput | GalaxyEmotionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing GalaxyEmotions.
     */
    cursor?: GalaxyEmotionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GalaxyEmotions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GalaxyEmotions.
     */
    skip?: number
    distinct?: GalaxyEmotionScalarFieldEnum | GalaxyEmotionScalarFieldEnum[]
  }

  /**
   * GalaxyEmotion create
   */
  export type GalaxyEmotionCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * The data needed to create a GalaxyEmotion.
     */
    data: XOR<GalaxyEmotionCreateInput, GalaxyEmotionUncheckedCreateInput>
  }

  /**
   * GalaxyEmotion createMany
   */
  export type GalaxyEmotionCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many GalaxyEmotions.
     */
    data: GalaxyEmotionCreateManyInput | GalaxyEmotionCreateManyInput[]
  }

  /**
   * GalaxyEmotion createManyAndReturn
   */
  export type GalaxyEmotionCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * The data used to create many GalaxyEmotions.
     */
    data: GalaxyEmotionCreateManyInput | GalaxyEmotionCreateManyInput[]
  }

  /**
   * GalaxyEmotion update
   */
  export type GalaxyEmotionUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * The data needed to update a GalaxyEmotion.
     */
    data: XOR<GalaxyEmotionUpdateInput, GalaxyEmotionUncheckedUpdateInput>
    /**
     * Choose, which GalaxyEmotion to update.
     */
    where: GalaxyEmotionWhereUniqueInput
  }

  /**
   * GalaxyEmotion updateMany
   */
  export type GalaxyEmotionUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update GalaxyEmotions.
     */
    data: XOR<GalaxyEmotionUpdateManyMutationInput, GalaxyEmotionUncheckedUpdateManyInput>
    /**
     * Filter which GalaxyEmotions to update
     */
    where?: GalaxyEmotionWhereInput
    /**
     * Limit how many GalaxyEmotions to update.
     */
    limit?: number
  }

  /**
   * GalaxyEmotion updateManyAndReturn
   */
  export type GalaxyEmotionUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * The data used to update GalaxyEmotions.
     */
    data: XOR<GalaxyEmotionUpdateManyMutationInput, GalaxyEmotionUncheckedUpdateManyInput>
    /**
     * Filter which GalaxyEmotions to update
     */
    where?: GalaxyEmotionWhereInput
    /**
     * Limit how many GalaxyEmotions to update.
     */
    limit?: number
  }

  /**
   * GalaxyEmotion upsert
   */
  export type GalaxyEmotionUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * The filter to search for the GalaxyEmotion to update in case it exists.
     */
    where: GalaxyEmotionWhereUniqueInput
    /**
     * In case the GalaxyEmotion found by the `where` argument doesn't exist, create a new GalaxyEmotion with this data.
     */
    create: XOR<GalaxyEmotionCreateInput, GalaxyEmotionUncheckedCreateInput>
    /**
     * In case the GalaxyEmotion was found with the provided `where` argument, update it with this data.
     */
    update: XOR<GalaxyEmotionUpdateInput, GalaxyEmotionUncheckedUpdateInput>
  }

  /**
   * GalaxyEmotion delete
   */
  export type GalaxyEmotionDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
    /**
     * Filter which GalaxyEmotion to delete.
     */
    where: GalaxyEmotionWhereUniqueInput
  }

  /**
   * GalaxyEmotion deleteMany
   */
  export type GalaxyEmotionDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which GalaxyEmotions to delete
     */
    where?: GalaxyEmotionWhereInput
    /**
     * Limit how many GalaxyEmotions to delete.
     */
    limit?: number
  }

  /**
   * GalaxyEmotion.resonances
   */
  export type GalaxyEmotion$resonancesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    where?: GalaxyResonanceWhereInput
    orderBy?: GalaxyResonanceOrderByWithRelationInput | GalaxyResonanceOrderByWithRelationInput[]
    cursor?: GalaxyResonanceWhereUniqueInput
    take?: number
    skip?: number
    distinct?: GalaxyResonanceScalarFieldEnum | GalaxyResonanceScalarFieldEnum[]
  }

  /**
   * GalaxyEmotion without action
   */
  export type GalaxyEmotionDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyEmotion
     */
    select?: GalaxyEmotionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyEmotion
     */
    omit?: GalaxyEmotionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyEmotionInclude<ExtArgs> | null
  }


  /**
   * Model GalaxyResonance
   */

  export type AggregateGalaxyResonance = {
    _count: GalaxyResonanceCountAggregateOutputType | null
    _min: GalaxyResonanceMinAggregateOutputType | null
    _max: GalaxyResonanceMaxAggregateOutputType | null
  }

  export type GalaxyResonanceMinAggregateOutputType = {
    id: string | null
    galaxyEmotionId: string | null
    userId: string | null
    type: $Enums.ResonanceType | null
    createdAt: Date | null
  }

  export type GalaxyResonanceMaxAggregateOutputType = {
    id: string | null
    galaxyEmotionId: string | null
    userId: string | null
    type: $Enums.ResonanceType | null
    createdAt: Date | null
  }

  export type GalaxyResonanceCountAggregateOutputType = {
    id: number
    galaxyEmotionId: number
    userId: number
    type: number
    createdAt: number
    _all: number
  }


  export type GalaxyResonanceMinAggregateInputType = {
    id?: true
    galaxyEmotionId?: true
    userId?: true
    type?: true
    createdAt?: true
  }

  export type GalaxyResonanceMaxAggregateInputType = {
    id?: true
    galaxyEmotionId?: true
    userId?: true
    type?: true
    createdAt?: true
  }

  export type GalaxyResonanceCountAggregateInputType = {
    id?: true
    galaxyEmotionId?: true
    userId?: true
    type?: true
    createdAt?: true
    _all?: true
  }

  export type GalaxyResonanceAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which GalaxyResonance to aggregate.
     */
    where?: GalaxyResonanceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GalaxyResonances to fetch.
     */
    orderBy?: GalaxyResonanceOrderByWithRelationInput | GalaxyResonanceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: GalaxyResonanceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GalaxyResonances from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GalaxyResonances.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned GalaxyResonances
    **/
    _count?: true | GalaxyResonanceCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: GalaxyResonanceMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: GalaxyResonanceMaxAggregateInputType
  }

  export type GetGalaxyResonanceAggregateType<T extends GalaxyResonanceAggregateArgs> = {
        [P in keyof T & keyof AggregateGalaxyResonance]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateGalaxyResonance[P]>
      : GetScalarType<T[P], AggregateGalaxyResonance[P]>
  }




  export type GalaxyResonanceGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GalaxyResonanceWhereInput
    orderBy?: GalaxyResonanceOrderByWithAggregationInput | GalaxyResonanceOrderByWithAggregationInput[]
    by: GalaxyResonanceScalarFieldEnum[] | GalaxyResonanceScalarFieldEnum
    having?: GalaxyResonanceScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: GalaxyResonanceCountAggregateInputType | true
    _min?: GalaxyResonanceMinAggregateInputType
    _max?: GalaxyResonanceMaxAggregateInputType
  }

  export type GalaxyResonanceGroupByOutputType = {
    id: string
    galaxyEmotionId: string
    userId: string
    type: $Enums.ResonanceType
    createdAt: Date
    _count: GalaxyResonanceCountAggregateOutputType | null
    _min: GalaxyResonanceMinAggregateOutputType | null
    _max: GalaxyResonanceMaxAggregateOutputType | null
  }

  type GetGalaxyResonanceGroupByPayload<T extends GalaxyResonanceGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<GalaxyResonanceGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof GalaxyResonanceGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], GalaxyResonanceGroupByOutputType[P]>
            : GetScalarType<T[P], GalaxyResonanceGroupByOutputType[P]>
        }
      >
    >


  export type GalaxyResonanceSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    galaxyEmotionId?: boolean
    userId?: boolean
    type?: boolean
    createdAt?: boolean
    galaxyEmotion?: boolean | GalaxyEmotionDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["galaxyResonance"]>

  export type GalaxyResonanceSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    galaxyEmotionId?: boolean
    userId?: boolean
    type?: boolean
    createdAt?: boolean
    galaxyEmotion?: boolean | GalaxyEmotionDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["galaxyResonance"]>

  export type GalaxyResonanceSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    galaxyEmotionId?: boolean
    userId?: boolean
    type?: boolean
    createdAt?: boolean
    galaxyEmotion?: boolean | GalaxyEmotionDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["galaxyResonance"]>

  export type GalaxyResonanceSelectScalar = {
    id?: boolean
    galaxyEmotionId?: boolean
    userId?: boolean
    type?: boolean
    createdAt?: boolean
  }

  export type GalaxyResonanceOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "galaxyEmotionId" | "userId" | "type" | "createdAt", ExtArgs["result"]["galaxyResonance"]>
  export type GalaxyResonanceInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    galaxyEmotion?: boolean | GalaxyEmotionDefaultArgs<ExtArgs>
  }
  export type GalaxyResonanceIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    galaxyEmotion?: boolean | GalaxyEmotionDefaultArgs<ExtArgs>
  }
  export type GalaxyResonanceIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    galaxyEmotion?: boolean | GalaxyEmotionDefaultArgs<ExtArgs>
  }

  export type $GalaxyResonancePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "GalaxyResonance"
    objects: {
      galaxyEmotion: Prisma.$GalaxyEmotionPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      galaxyEmotionId: string
      userId: string
      type: $Enums.ResonanceType
      createdAt: Date
    }, ExtArgs["result"]["galaxyResonance"]>
    composites: {}
  }

  type GalaxyResonanceGetPayload<S extends boolean | null | undefined | GalaxyResonanceDefaultArgs> = $Result.GetResult<Prisma.$GalaxyResonancePayload, S>

  type GalaxyResonanceCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<GalaxyResonanceFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: GalaxyResonanceCountAggregateInputType | true
    }

  export interface GalaxyResonanceDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['GalaxyResonance'], meta: { name: 'GalaxyResonance' } }
    /**
     * Find zero or one GalaxyResonance that matches the filter.
     * @param {GalaxyResonanceFindUniqueArgs} args - Arguments to find a GalaxyResonance
     * @example
     * // Get one GalaxyResonance
     * const galaxyResonance = await prisma.galaxyResonance.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends GalaxyResonanceFindUniqueArgs>(args: SelectSubset<T, GalaxyResonanceFindUniqueArgs<ExtArgs>>): Prisma__GalaxyResonanceClient<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one GalaxyResonance that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {GalaxyResonanceFindUniqueOrThrowArgs} args - Arguments to find a GalaxyResonance
     * @example
     * // Get one GalaxyResonance
     * const galaxyResonance = await prisma.galaxyResonance.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends GalaxyResonanceFindUniqueOrThrowArgs>(args: SelectSubset<T, GalaxyResonanceFindUniqueOrThrowArgs<ExtArgs>>): Prisma__GalaxyResonanceClient<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first GalaxyResonance that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyResonanceFindFirstArgs} args - Arguments to find a GalaxyResonance
     * @example
     * // Get one GalaxyResonance
     * const galaxyResonance = await prisma.galaxyResonance.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends GalaxyResonanceFindFirstArgs>(args?: SelectSubset<T, GalaxyResonanceFindFirstArgs<ExtArgs>>): Prisma__GalaxyResonanceClient<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first GalaxyResonance that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyResonanceFindFirstOrThrowArgs} args - Arguments to find a GalaxyResonance
     * @example
     * // Get one GalaxyResonance
     * const galaxyResonance = await prisma.galaxyResonance.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends GalaxyResonanceFindFirstOrThrowArgs>(args?: SelectSubset<T, GalaxyResonanceFindFirstOrThrowArgs<ExtArgs>>): Prisma__GalaxyResonanceClient<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more GalaxyResonances that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyResonanceFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all GalaxyResonances
     * const galaxyResonances = await prisma.galaxyResonance.findMany()
     * 
     * // Get first 10 GalaxyResonances
     * const galaxyResonances = await prisma.galaxyResonance.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const galaxyResonanceWithIdOnly = await prisma.galaxyResonance.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends GalaxyResonanceFindManyArgs>(args?: SelectSubset<T, GalaxyResonanceFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a GalaxyResonance.
     * @param {GalaxyResonanceCreateArgs} args - Arguments to create a GalaxyResonance.
     * @example
     * // Create one GalaxyResonance
     * const GalaxyResonance = await prisma.galaxyResonance.create({
     *   data: {
     *     // ... data to create a GalaxyResonance
     *   }
     * })
     * 
     */
    create<T extends GalaxyResonanceCreateArgs>(args: SelectSubset<T, GalaxyResonanceCreateArgs<ExtArgs>>): Prisma__GalaxyResonanceClient<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many GalaxyResonances.
     * @param {GalaxyResonanceCreateManyArgs} args - Arguments to create many GalaxyResonances.
     * @example
     * // Create many GalaxyResonances
     * const galaxyResonance = await prisma.galaxyResonance.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends GalaxyResonanceCreateManyArgs>(args?: SelectSubset<T, GalaxyResonanceCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many GalaxyResonances and returns the data saved in the database.
     * @param {GalaxyResonanceCreateManyAndReturnArgs} args - Arguments to create many GalaxyResonances.
     * @example
     * // Create many GalaxyResonances
     * const galaxyResonance = await prisma.galaxyResonance.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many GalaxyResonances and only return the `id`
     * const galaxyResonanceWithIdOnly = await prisma.galaxyResonance.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends GalaxyResonanceCreateManyAndReturnArgs>(args?: SelectSubset<T, GalaxyResonanceCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a GalaxyResonance.
     * @param {GalaxyResonanceDeleteArgs} args - Arguments to delete one GalaxyResonance.
     * @example
     * // Delete one GalaxyResonance
     * const GalaxyResonance = await prisma.galaxyResonance.delete({
     *   where: {
     *     // ... filter to delete one GalaxyResonance
     *   }
     * })
     * 
     */
    delete<T extends GalaxyResonanceDeleteArgs>(args: SelectSubset<T, GalaxyResonanceDeleteArgs<ExtArgs>>): Prisma__GalaxyResonanceClient<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one GalaxyResonance.
     * @param {GalaxyResonanceUpdateArgs} args - Arguments to update one GalaxyResonance.
     * @example
     * // Update one GalaxyResonance
     * const galaxyResonance = await prisma.galaxyResonance.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends GalaxyResonanceUpdateArgs>(args: SelectSubset<T, GalaxyResonanceUpdateArgs<ExtArgs>>): Prisma__GalaxyResonanceClient<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more GalaxyResonances.
     * @param {GalaxyResonanceDeleteManyArgs} args - Arguments to filter GalaxyResonances to delete.
     * @example
     * // Delete a few GalaxyResonances
     * const { count } = await prisma.galaxyResonance.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends GalaxyResonanceDeleteManyArgs>(args?: SelectSubset<T, GalaxyResonanceDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more GalaxyResonances.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyResonanceUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many GalaxyResonances
     * const galaxyResonance = await prisma.galaxyResonance.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends GalaxyResonanceUpdateManyArgs>(args: SelectSubset<T, GalaxyResonanceUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more GalaxyResonances and returns the data updated in the database.
     * @param {GalaxyResonanceUpdateManyAndReturnArgs} args - Arguments to update many GalaxyResonances.
     * @example
     * // Update many GalaxyResonances
     * const galaxyResonance = await prisma.galaxyResonance.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more GalaxyResonances and only return the `id`
     * const galaxyResonanceWithIdOnly = await prisma.galaxyResonance.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends GalaxyResonanceUpdateManyAndReturnArgs>(args: SelectSubset<T, GalaxyResonanceUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one GalaxyResonance.
     * @param {GalaxyResonanceUpsertArgs} args - Arguments to update or create a GalaxyResonance.
     * @example
     * // Update or create a GalaxyResonance
     * const galaxyResonance = await prisma.galaxyResonance.upsert({
     *   create: {
     *     // ... data to create a GalaxyResonance
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the GalaxyResonance we want to update
     *   }
     * })
     */
    upsert<T extends GalaxyResonanceUpsertArgs>(args: SelectSubset<T, GalaxyResonanceUpsertArgs<ExtArgs>>): Prisma__GalaxyResonanceClient<$Result.GetResult<Prisma.$GalaxyResonancePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of GalaxyResonances.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyResonanceCountArgs} args - Arguments to filter GalaxyResonances to count.
     * @example
     * // Count the number of GalaxyResonances
     * const count = await prisma.galaxyResonance.count({
     *   where: {
     *     // ... the filter for the GalaxyResonances we want to count
     *   }
     * })
    **/
    count<T extends GalaxyResonanceCountArgs>(
      args?: Subset<T, GalaxyResonanceCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], GalaxyResonanceCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a GalaxyResonance.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyResonanceAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends GalaxyResonanceAggregateArgs>(args: Subset<T, GalaxyResonanceAggregateArgs>): Prisma.PrismaPromise<GetGalaxyResonanceAggregateType<T>>

    /**
     * Group by GalaxyResonance.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GalaxyResonanceGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends GalaxyResonanceGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: GalaxyResonanceGroupByArgs['orderBy'] }
        : { orderBy?: GalaxyResonanceGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, GalaxyResonanceGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGalaxyResonanceGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the GalaxyResonance model
   */
  readonly fields: GalaxyResonanceFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for GalaxyResonance.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__GalaxyResonanceClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    galaxyEmotion<T extends GalaxyEmotionDefaultArgs<ExtArgs> = {}>(args?: Subset<T, GalaxyEmotionDefaultArgs<ExtArgs>>): Prisma__GalaxyEmotionClient<$Result.GetResult<Prisma.$GalaxyEmotionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the GalaxyResonance model
   */
  interface GalaxyResonanceFieldRefs {
    readonly id: FieldRef<"GalaxyResonance", 'String'>
    readonly galaxyEmotionId: FieldRef<"GalaxyResonance", 'String'>
    readonly userId: FieldRef<"GalaxyResonance", 'String'>
    readonly type: FieldRef<"GalaxyResonance", 'ResonanceType'>
    readonly createdAt: FieldRef<"GalaxyResonance", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * GalaxyResonance findUnique
   */
  export type GalaxyResonanceFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyResonance to fetch.
     */
    where: GalaxyResonanceWhereUniqueInput
  }

  /**
   * GalaxyResonance findUniqueOrThrow
   */
  export type GalaxyResonanceFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyResonance to fetch.
     */
    where: GalaxyResonanceWhereUniqueInput
  }

  /**
   * GalaxyResonance findFirst
   */
  export type GalaxyResonanceFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyResonance to fetch.
     */
    where?: GalaxyResonanceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GalaxyResonances to fetch.
     */
    orderBy?: GalaxyResonanceOrderByWithRelationInput | GalaxyResonanceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for GalaxyResonances.
     */
    cursor?: GalaxyResonanceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GalaxyResonances from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GalaxyResonances.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of GalaxyResonances.
     */
    distinct?: GalaxyResonanceScalarFieldEnum | GalaxyResonanceScalarFieldEnum[]
  }

  /**
   * GalaxyResonance findFirstOrThrow
   */
  export type GalaxyResonanceFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyResonance to fetch.
     */
    where?: GalaxyResonanceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GalaxyResonances to fetch.
     */
    orderBy?: GalaxyResonanceOrderByWithRelationInput | GalaxyResonanceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for GalaxyResonances.
     */
    cursor?: GalaxyResonanceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GalaxyResonances from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GalaxyResonances.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of GalaxyResonances.
     */
    distinct?: GalaxyResonanceScalarFieldEnum | GalaxyResonanceScalarFieldEnum[]
  }

  /**
   * GalaxyResonance findMany
   */
  export type GalaxyResonanceFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * Filter, which GalaxyResonances to fetch.
     */
    where?: GalaxyResonanceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GalaxyResonances to fetch.
     */
    orderBy?: GalaxyResonanceOrderByWithRelationInput | GalaxyResonanceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing GalaxyResonances.
     */
    cursor?: GalaxyResonanceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GalaxyResonances from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GalaxyResonances.
     */
    skip?: number
    distinct?: GalaxyResonanceScalarFieldEnum | GalaxyResonanceScalarFieldEnum[]
  }

  /**
   * GalaxyResonance create
   */
  export type GalaxyResonanceCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * The data needed to create a GalaxyResonance.
     */
    data: XOR<GalaxyResonanceCreateInput, GalaxyResonanceUncheckedCreateInput>
  }

  /**
   * GalaxyResonance createMany
   */
  export type GalaxyResonanceCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many GalaxyResonances.
     */
    data: GalaxyResonanceCreateManyInput | GalaxyResonanceCreateManyInput[]
  }

  /**
   * GalaxyResonance createManyAndReturn
   */
  export type GalaxyResonanceCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * The data used to create many GalaxyResonances.
     */
    data: GalaxyResonanceCreateManyInput | GalaxyResonanceCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * GalaxyResonance update
   */
  export type GalaxyResonanceUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * The data needed to update a GalaxyResonance.
     */
    data: XOR<GalaxyResonanceUpdateInput, GalaxyResonanceUncheckedUpdateInput>
    /**
     * Choose, which GalaxyResonance to update.
     */
    where: GalaxyResonanceWhereUniqueInput
  }

  /**
   * GalaxyResonance updateMany
   */
  export type GalaxyResonanceUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update GalaxyResonances.
     */
    data: XOR<GalaxyResonanceUpdateManyMutationInput, GalaxyResonanceUncheckedUpdateManyInput>
    /**
     * Filter which GalaxyResonances to update
     */
    where?: GalaxyResonanceWhereInput
    /**
     * Limit how many GalaxyResonances to update.
     */
    limit?: number
  }

  /**
   * GalaxyResonance updateManyAndReturn
   */
  export type GalaxyResonanceUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * The data used to update GalaxyResonances.
     */
    data: XOR<GalaxyResonanceUpdateManyMutationInput, GalaxyResonanceUncheckedUpdateManyInput>
    /**
     * Filter which GalaxyResonances to update
     */
    where?: GalaxyResonanceWhereInput
    /**
     * Limit how many GalaxyResonances to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * GalaxyResonance upsert
   */
  export type GalaxyResonanceUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * The filter to search for the GalaxyResonance to update in case it exists.
     */
    where: GalaxyResonanceWhereUniqueInput
    /**
     * In case the GalaxyResonance found by the `where` argument doesn't exist, create a new GalaxyResonance with this data.
     */
    create: XOR<GalaxyResonanceCreateInput, GalaxyResonanceUncheckedCreateInput>
    /**
     * In case the GalaxyResonance was found with the provided `where` argument, update it with this data.
     */
    update: XOR<GalaxyResonanceUpdateInput, GalaxyResonanceUncheckedUpdateInput>
  }

  /**
   * GalaxyResonance delete
   */
  export type GalaxyResonanceDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
    /**
     * Filter which GalaxyResonance to delete.
     */
    where: GalaxyResonanceWhereUniqueInput
  }

  /**
   * GalaxyResonance deleteMany
   */
  export type GalaxyResonanceDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which GalaxyResonances to delete
     */
    where?: GalaxyResonanceWhereInput
    /**
     * Limit how many GalaxyResonances to delete.
     */
    limit?: number
  }

  /**
   * GalaxyResonance without action
   */
  export type GalaxyResonanceDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GalaxyResonance
     */
    select?: GalaxyResonanceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GalaxyResonance
     */
    omit?: GalaxyResonanceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GalaxyResonanceInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    email: 'email',
    username: 'username',
    password: 'password',
    avatar: 'avatar',
    bio: 'bio',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const SessionScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    token: 'token',
    expiresAt: 'expiresAt',
    createdAt: 'createdAt'
  };

  export type SessionScalarFieldEnum = (typeof SessionScalarFieldEnum)[keyof typeof SessionScalarFieldEnum]


  export const StarScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    title: 'title',
    content: 'content',
    type: 'type',
    mediaUrl: 'mediaUrl',
    emotion: 'emotion',
    brightness: 'brightness',
    isSealed: 'isSealed',
    sealedUntil: 'sealedUntil',
    position: 'position',
    color: 'color',
    size: 'size',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type StarScalarFieldEnum = (typeof StarScalarFieldEnum)[keyof typeof StarScalarFieldEnum]


  export const ResonanceScalarFieldEnum: {
    id: 'id',
    starId: 'starId',
    userId: 'userId',
    type: 'type',
    message: 'message',
    isAnonymous: 'isAnonymous',
    createdAt: 'createdAt'
  };

  export type ResonanceScalarFieldEnum = (typeof ResonanceScalarFieldEnum)[keyof typeof ResonanceScalarFieldEnum]


  export const GalaxyEmotionScalarFieldEnum: {
    id: 'id',
    content: 'content',
    emotion: 'emotion',
    intensity: 'intensity',
    position: 'position',
    color: 'color',
    size: 'size',
    expiresAt: 'expiresAt',
    createdAt: 'createdAt'
  };

  export type GalaxyEmotionScalarFieldEnum = (typeof GalaxyEmotionScalarFieldEnum)[keyof typeof GalaxyEmotionScalarFieldEnum]


  export const GalaxyResonanceScalarFieldEnum: {
    id: 'id',
    galaxyEmotionId: 'galaxyEmotionId',
    userId: 'userId',
    type: 'type',
    createdAt: 'createdAt'
  };

  export type GalaxyResonanceScalarFieldEnum = (typeof GalaxyResonanceScalarFieldEnum)[keyof typeof GalaxyResonanceScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const JsonNullValueInput: {
    JsonNull: typeof JsonNull
  };

  export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'StarType'
   */
  export type EnumStarTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'StarType'>
    


  /**
   * Reference to a field of type 'Emotion'
   */
  export type EnumEmotionFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Emotion'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


  /**
   * Reference to a field of type 'ResonanceType'
   */
  export type EnumResonanceTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ResonanceType'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: StringFilter<"User"> | string
    email?: StringFilter<"User"> | string
    username?: StringFilter<"User"> | string
    password?: StringFilter<"User"> | string
    avatar?: StringNullableFilter<"User"> | string | null
    bio?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    stars?: StarListRelationFilter
    resonances?: ResonanceListRelationFilter
    sessions?: SessionListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    avatar?: SortOrderInput | SortOrder
    bio?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    stars?: StarOrderByRelationAggregateInput
    resonances?: ResonanceOrderByRelationAggregateInput
    sessions?: SessionOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    username?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    password?: StringFilter<"User"> | string
    avatar?: StringNullableFilter<"User"> | string | null
    bio?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    stars?: StarListRelationFilter
    resonances?: ResonanceListRelationFilter
    sessions?: SessionListRelationFilter
  }, "id" | "email" | "username">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    avatar?: SortOrderInput | SortOrder
    bio?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"User"> | string
    email?: StringWithAggregatesFilter<"User"> | string
    username?: StringWithAggregatesFilter<"User"> | string
    password?: StringWithAggregatesFilter<"User"> | string
    avatar?: StringNullableWithAggregatesFilter<"User"> | string | null
    bio?: StringNullableWithAggregatesFilter<"User"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type SessionWhereInput = {
    AND?: SessionWhereInput | SessionWhereInput[]
    OR?: SessionWhereInput[]
    NOT?: SessionWhereInput | SessionWhereInput[]
    id?: StringFilter<"Session"> | string
    userId?: StringFilter<"Session"> | string
    token?: StringFilter<"Session"> | string
    expiresAt?: DateTimeFilter<"Session"> | Date | string
    createdAt?: DateTimeFilter<"Session"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }

  export type SessionOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    token?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type SessionWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    token?: string
    AND?: SessionWhereInput | SessionWhereInput[]
    OR?: SessionWhereInput[]
    NOT?: SessionWhereInput | SessionWhereInput[]
    userId?: StringFilter<"Session"> | string
    expiresAt?: DateTimeFilter<"Session"> | Date | string
    createdAt?: DateTimeFilter<"Session"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }, "id" | "token">

  export type SessionOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    token?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    _count?: SessionCountOrderByAggregateInput
    _max?: SessionMaxOrderByAggregateInput
    _min?: SessionMinOrderByAggregateInput
  }

  export type SessionScalarWhereWithAggregatesInput = {
    AND?: SessionScalarWhereWithAggregatesInput | SessionScalarWhereWithAggregatesInput[]
    OR?: SessionScalarWhereWithAggregatesInput[]
    NOT?: SessionScalarWhereWithAggregatesInput | SessionScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Session"> | string
    userId?: StringWithAggregatesFilter<"Session"> | string
    token?: StringWithAggregatesFilter<"Session"> | string
    expiresAt?: DateTimeWithAggregatesFilter<"Session"> | Date | string
    createdAt?: DateTimeWithAggregatesFilter<"Session"> | Date | string
  }

  export type StarWhereInput = {
    AND?: StarWhereInput | StarWhereInput[]
    OR?: StarWhereInput[]
    NOT?: StarWhereInput | StarWhereInput[]
    id?: StringFilter<"Star"> | string
    userId?: StringFilter<"Star"> | string
    title?: StringNullableFilter<"Star"> | string | null
    content?: StringFilter<"Star"> | string
    type?: EnumStarTypeFilter<"Star"> | $Enums.StarType
    mediaUrl?: StringNullableFilter<"Star"> | string | null
    emotion?: EnumEmotionFilter<"Star"> | $Enums.Emotion
    brightness?: FloatFilter<"Star"> | number
    isSealed?: BoolFilter<"Star"> | boolean
    sealedUntil?: DateTimeNullableFilter<"Star"> | Date | string | null
    position?: JsonNullableFilter<"Star">
    color?: StringNullableFilter<"Star"> | string | null
    size?: FloatFilter<"Star"> | number
    createdAt?: DateTimeFilter<"Star"> | Date | string
    updatedAt?: DateTimeFilter<"Star"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
    resonances?: ResonanceListRelationFilter
  }

  export type StarOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrderInput | SortOrder
    content?: SortOrder
    type?: SortOrder
    mediaUrl?: SortOrderInput | SortOrder
    emotion?: SortOrder
    brightness?: SortOrder
    isSealed?: SortOrder
    sealedUntil?: SortOrderInput | SortOrder
    position?: SortOrderInput | SortOrder
    color?: SortOrderInput | SortOrder
    size?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: UserOrderByWithRelationInput
    resonances?: ResonanceOrderByRelationAggregateInput
  }

  export type StarWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: StarWhereInput | StarWhereInput[]
    OR?: StarWhereInput[]
    NOT?: StarWhereInput | StarWhereInput[]
    userId?: StringFilter<"Star"> | string
    title?: StringNullableFilter<"Star"> | string | null
    content?: StringFilter<"Star"> | string
    type?: EnumStarTypeFilter<"Star"> | $Enums.StarType
    mediaUrl?: StringNullableFilter<"Star"> | string | null
    emotion?: EnumEmotionFilter<"Star"> | $Enums.Emotion
    brightness?: FloatFilter<"Star"> | number
    isSealed?: BoolFilter<"Star"> | boolean
    sealedUntil?: DateTimeNullableFilter<"Star"> | Date | string | null
    position?: JsonNullableFilter<"Star">
    color?: StringNullableFilter<"Star"> | string | null
    size?: FloatFilter<"Star"> | number
    createdAt?: DateTimeFilter<"Star"> | Date | string
    updatedAt?: DateTimeFilter<"Star"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
    resonances?: ResonanceListRelationFilter
  }, "id">

  export type StarOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrderInput | SortOrder
    content?: SortOrder
    type?: SortOrder
    mediaUrl?: SortOrderInput | SortOrder
    emotion?: SortOrder
    brightness?: SortOrder
    isSealed?: SortOrder
    sealedUntil?: SortOrderInput | SortOrder
    position?: SortOrderInput | SortOrder
    color?: SortOrderInput | SortOrder
    size?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: StarCountOrderByAggregateInput
    _avg?: StarAvgOrderByAggregateInput
    _max?: StarMaxOrderByAggregateInput
    _min?: StarMinOrderByAggregateInput
    _sum?: StarSumOrderByAggregateInput
  }

  export type StarScalarWhereWithAggregatesInput = {
    AND?: StarScalarWhereWithAggregatesInput | StarScalarWhereWithAggregatesInput[]
    OR?: StarScalarWhereWithAggregatesInput[]
    NOT?: StarScalarWhereWithAggregatesInput | StarScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Star"> | string
    userId?: StringWithAggregatesFilter<"Star"> | string
    title?: StringNullableWithAggregatesFilter<"Star"> | string | null
    content?: StringWithAggregatesFilter<"Star"> | string
    type?: EnumStarTypeWithAggregatesFilter<"Star"> | $Enums.StarType
    mediaUrl?: StringNullableWithAggregatesFilter<"Star"> | string | null
    emotion?: EnumEmotionWithAggregatesFilter<"Star"> | $Enums.Emotion
    brightness?: FloatWithAggregatesFilter<"Star"> | number
    isSealed?: BoolWithAggregatesFilter<"Star"> | boolean
    sealedUntil?: DateTimeNullableWithAggregatesFilter<"Star"> | Date | string | null
    position?: JsonNullableWithAggregatesFilter<"Star">
    color?: StringNullableWithAggregatesFilter<"Star"> | string | null
    size?: FloatWithAggregatesFilter<"Star"> | number
    createdAt?: DateTimeWithAggregatesFilter<"Star"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Star"> | Date | string
  }

  export type ResonanceWhereInput = {
    AND?: ResonanceWhereInput | ResonanceWhereInput[]
    OR?: ResonanceWhereInput[]
    NOT?: ResonanceWhereInput | ResonanceWhereInput[]
    id?: StringFilter<"Resonance"> | string
    starId?: StringFilter<"Resonance"> | string
    userId?: StringFilter<"Resonance"> | string
    type?: EnumResonanceTypeFilter<"Resonance"> | $Enums.ResonanceType
    message?: StringNullableFilter<"Resonance"> | string | null
    isAnonymous?: BoolFilter<"Resonance"> | boolean
    createdAt?: DateTimeFilter<"Resonance"> | Date | string
    star?: XOR<StarScalarRelationFilter, StarWhereInput>
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }

  export type ResonanceOrderByWithRelationInput = {
    id?: SortOrder
    starId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    message?: SortOrderInput | SortOrder
    isAnonymous?: SortOrder
    createdAt?: SortOrder
    star?: StarOrderByWithRelationInput
    user?: UserOrderByWithRelationInput
  }

  export type ResonanceWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    starId_userId?: ResonanceStarIdUserIdCompoundUniqueInput
    AND?: ResonanceWhereInput | ResonanceWhereInput[]
    OR?: ResonanceWhereInput[]
    NOT?: ResonanceWhereInput | ResonanceWhereInput[]
    starId?: StringFilter<"Resonance"> | string
    userId?: StringFilter<"Resonance"> | string
    type?: EnumResonanceTypeFilter<"Resonance"> | $Enums.ResonanceType
    message?: StringNullableFilter<"Resonance"> | string | null
    isAnonymous?: BoolFilter<"Resonance"> | boolean
    createdAt?: DateTimeFilter<"Resonance"> | Date | string
    star?: XOR<StarScalarRelationFilter, StarWhereInput>
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }, "id" | "starId_userId">

  export type ResonanceOrderByWithAggregationInput = {
    id?: SortOrder
    starId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    message?: SortOrderInput | SortOrder
    isAnonymous?: SortOrder
    createdAt?: SortOrder
    _count?: ResonanceCountOrderByAggregateInput
    _max?: ResonanceMaxOrderByAggregateInput
    _min?: ResonanceMinOrderByAggregateInput
  }

  export type ResonanceScalarWhereWithAggregatesInput = {
    AND?: ResonanceScalarWhereWithAggregatesInput | ResonanceScalarWhereWithAggregatesInput[]
    OR?: ResonanceScalarWhereWithAggregatesInput[]
    NOT?: ResonanceScalarWhereWithAggregatesInput | ResonanceScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Resonance"> | string
    starId?: StringWithAggregatesFilter<"Resonance"> | string
    userId?: StringWithAggregatesFilter<"Resonance"> | string
    type?: EnumResonanceTypeWithAggregatesFilter<"Resonance"> | $Enums.ResonanceType
    message?: StringNullableWithAggregatesFilter<"Resonance"> | string | null
    isAnonymous?: BoolWithAggregatesFilter<"Resonance"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"Resonance"> | Date | string
  }

  export type GalaxyEmotionWhereInput = {
    AND?: GalaxyEmotionWhereInput | GalaxyEmotionWhereInput[]
    OR?: GalaxyEmotionWhereInput[]
    NOT?: GalaxyEmotionWhereInput | GalaxyEmotionWhereInput[]
    id?: StringFilter<"GalaxyEmotion"> | string
    content?: StringFilter<"GalaxyEmotion"> | string
    emotion?: EnumEmotionFilter<"GalaxyEmotion"> | $Enums.Emotion
    intensity?: FloatFilter<"GalaxyEmotion"> | number
    position?: JsonFilter<"GalaxyEmotion">
    color?: StringFilter<"GalaxyEmotion"> | string
    size?: FloatFilter<"GalaxyEmotion"> | number
    expiresAt?: DateTimeFilter<"GalaxyEmotion"> | Date | string
    createdAt?: DateTimeFilter<"GalaxyEmotion"> | Date | string
    resonances?: GalaxyResonanceListRelationFilter
  }

  export type GalaxyEmotionOrderByWithRelationInput = {
    id?: SortOrder
    content?: SortOrder
    emotion?: SortOrder
    intensity?: SortOrder
    position?: SortOrder
    color?: SortOrder
    size?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    resonances?: GalaxyResonanceOrderByRelationAggregateInput
  }

  export type GalaxyEmotionWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: GalaxyEmotionWhereInput | GalaxyEmotionWhereInput[]
    OR?: GalaxyEmotionWhereInput[]
    NOT?: GalaxyEmotionWhereInput | GalaxyEmotionWhereInput[]
    content?: StringFilter<"GalaxyEmotion"> | string
    emotion?: EnumEmotionFilter<"GalaxyEmotion"> | $Enums.Emotion
    intensity?: FloatFilter<"GalaxyEmotion"> | number
    position?: JsonFilter<"GalaxyEmotion">
    color?: StringFilter<"GalaxyEmotion"> | string
    size?: FloatFilter<"GalaxyEmotion"> | number
    expiresAt?: DateTimeFilter<"GalaxyEmotion"> | Date | string
    createdAt?: DateTimeFilter<"GalaxyEmotion"> | Date | string
    resonances?: GalaxyResonanceListRelationFilter
  }, "id">

  export type GalaxyEmotionOrderByWithAggregationInput = {
    id?: SortOrder
    content?: SortOrder
    emotion?: SortOrder
    intensity?: SortOrder
    position?: SortOrder
    color?: SortOrder
    size?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
    _count?: GalaxyEmotionCountOrderByAggregateInput
    _avg?: GalaxyEmotionAvgOrderByAggregateInput
    _max?: GalaxyEmotionMaxOrderByAggregateInput
    _min?: GalaxyEmotionMinOrderByAggregateInput
    _sum?: GalaxyEmotionSumOrderByAggregateInput
  }

  export type GalaxyEmotionScalarWhereWithAggregatesInput = {
    AND?: GalaxyEmotionScalarWhereWithAggregatesInput | GalaxyEmotionScalarWhereWithAggregatesInput[]
    OR?: GalaxyEmotionScalarWhereWithAggregatesInput[]
    NOT?: GalaxyEmotionScalarWhereWithAggregatesInput | GalaxyEmotionScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"GalaxyEmotion"> | string
    content?: StringWithAggregatesFilter<"GalaxyEmotion"> | string
    emotion?: EnumEmotionWithAggregatesFilter<"GalaxyEmotion"> | $Enums.Emotion
    intensity?: FloatWithAggregatesFilter<"GalaxyEmotion"> | number
    position?: JsonWithAggregatesFilter<"GalaxyEmotion">
    color?: StringWithAggregatesFilter<"GalaxyEmotion"> | string
    size?: FloatWithAggregatesFilter<"GalaxyEmotion"> | number
    expiresAt?: DateTimeWithAggregatesFilter<"GalaxyEmotion"> | Date | string
    createdAt?: DateTimeWithAggregatesFilter<"GalaxyEmotion"> | Date | string
  }

  export type GalaxyResonanceWhereInput = {
    AND?: GalaxyResonanceWhereInput | GalaxyResonanceWhereInput[]
    OR?: GalaxyResonanceWhereInput[]
    NOT?: GalaxyResonanceWhereInput | GalaxyResonanceWhereInput[]
    id?: StringFilter<"GalaxyResonance"> | string
    galaxyEmotionId?: StringFilter<"GalaxyResonance"> | string
    userId?: StringFilter<"GalaxyResonance"> | string
    type?: EnumResonanceTypeFilter<"GalaxyResonance"> | $Enums.ResonanceType
    createdAt?: DateTimeFilter<"GalaxyResonance"> | Date | string
    galaxyEmotion?: XOR<GalaxyEmotionScalarRelationFilter, GalaxyEmotionWhereInput>
  }

  export type GalaxyResonanceOrderByWithRelationInput = {
    id?: SortOrder
    galaxyEmotionId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
    galaxyEmotion?: GalaxyEmotionOrderByWithRelationInput
  }

  export type GalaxyResonanceWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    galaxyEmotionId_userId?: GalaxyResonanceGalaxyEmotionIdUserIdCompoundUniqueInput
    AND?: GalaxyResonanceWhereInput | GalaxyResonanceWhereInput[]
    OR?: GalaxyResonanceWhereInput[]
    NOT?: GalaxyResonanceWhereInput | GalaxyResonanceWhereInput[]
    galaxyEmotionId?: StringFilter<"GalaxyResonance"> | string
    userId?: StringFilter<"GalaxyResonance"> | string
    type?: EnumResonanceTypeFilter<"GalaxyResonance"> | $Enums.ResonanceType
    createdAt?: DateTimeFilter<"GalaxyResonance"> | Date | string
    galaxyEmotion?: XOR<GalaxyEmotionScalarRelationFilter, GalaxyEmotionWhereInput>
  }, "id" | "galaxyEmotionId_userId">

  export type GalaxyResonanceOrderByWithAggregationInput = {
    id?: SortOrder
    galaxyEmotionId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
    _count?: GalaxyResonanceCountOrderByAggregateInput
    _max?: GalaxyResonanceMaxOrderByAggregateInput
    _min?: GalaxyResonanceMinOrderByAggregateInput
  }

  export type GalaxyResonanceScalarWhereWithAggregatesInput = {
    AND?: GalaxyResonanceScalarWhereWithAggregatesInput | GalaxyResonanceScalarWhereWithAggregatesInput[]
    OR?: GalaxyResonanceScalarWhereWithAggregatesInput[]
    NOT?: GalaxyResonanceScalarWhereWithAggregatesInput | GalaxyResonanceScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"GalaxyResonance"> | string
    galaxyEmotionId?: StringWithAggregatesFilter<"GalaxyResonance"> | string
    userId?: StringWithAggregatesFilter<"GalaxyResonance"> | string
    type?: EnumResonanceTypeWithAggregatesFilter<"GalaxyResonance"> | $Enums.ResonanceType
    createdAt?: DateTimeWithAggregatesFilter<"GalaxyResonance"> | Date | string
  }

  export type UserCreateInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    stars?: StarCreateNestedManyWithoutUserInput
    resonances?: ResonanceCreateNestedManyWithoutUserInput
    sessions?: SessionCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    stars?: StarUncheckedCreateNestedManyWithoutUserInput
    resonances?: ResonanceUncheckedCreateNestedManyWithoutUserInput
    sessions?: SessionUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    stars?: StarUpdateManyWithoutUserNestedInput
    resonances?: ResonanceUpdateManyWithoutUserNestedInput
    sessions?: SessionUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    stars?: StarUncheckedUpdateManyWithoutUserNestedInput
    resonances?: ResonanceUncheckedUpdateManyWithoutUserNestedInput
    sessions?: SessionUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionCreateInput = {
    id?: string
    token: string
    expiresAt: Date | string
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutSessionsInput
  }

  export type SessionUncheckedCreateInput = {
    id?: string
    userId: string
    token: string
    expiresAt: Date | string
    createdAt?: Date | string
  }

  export type SessionUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    token?: StringFieldUpdateOperationsInput | string
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutSessionsNestedInput
  }

  export type SessionUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    token?: StringFieldUpdateOperationsInput | string
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionCreateManyInput = {
    id?: string
    userId: string
    token: string
    expiresAt: Date | string
    createdAt?: Date | string
  }

  export type SessionUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    token?: StringFieldUpdateOperationsInput | string
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    token?: StringFieldUpdateOperationsInput | string
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StarCreateInput = {
    id?: string
    title?: string | null
    content: string
    type?: $Enums.StarType
    mediaUrl?: string | null
    emotion?: $Enums.Emotion
    brightness?: number
    isSealed?: boolean
    sealedUntil?: Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: string | null
    size?: number
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutStarsInput
    resonances?: ResonanceCreateNestedManyWithoutStarInput
  }

  export type StarUncheckedCreateInput = {
    id?: string
    userId: string
    title?: string | null
    content: string
    type?: $Enums.StarType
    mediaUrl?: string | null
    emotion?: $Enums.Emotion
    brightness?: number
    isSealed?: boolean
    sealedUntil?: Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: string | null
    size?: number
    createdAt?: Date | string
    updatedAt?: Date | string
    resonances?: ResonanceUncheckedCreateNestedManyWithoutStarInput
  }

  export type StarUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutStarsNestedInput
    resonances?: ResonanceUpdateManyWithoutStarNestedInput
  }

  export type StarUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resonances?: ResonanceUncheckedUpdateManyWithoutStarNestedInput
  }

  export type StarCreateManyInput = {
    id?: string
    userId: string
    title?: string | null
    content: string
    type?: $Enums.StarType
    mediaUrl?: string | null
    emotion?: $Enums.Emotion
    brightness?: number
    isSealed?: boolean
    sealedUntil?: Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: string | null
    size?: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StarUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StarUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ResonanceCreateInput = {
    id?: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
    star: StarCreateNestedOneWithoutResonancesInput
    user: UserCreateNestedOneWithoutResonancesInput
  }

  export type ResonanceUncheckedCreateInput = {
    id?: string
    starId: string
    userId: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
  }

  export type ResonanceUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    star?: StarUpdateOneRequiredWithoutResonancesNestedInput
    user?: UserUpdateOneRequiredWithoutResonancesNestedInput
  }

  export type ResonanceUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    starId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ResonanceCreateManyInput = {
    id?: string
    starId: string
    userId: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
  }

  export type ResonanceUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ResonanceUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    starId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyEmotionCreateInput = {
    id?: string
    content: string
    emotion: $Enums.Emotion
    intensity?: number
    position: JsonNullValueInput | InputJsonValue
    color: string
    size?: number
    expiresAt: Date | string
    createdAt?: Date | string
    resonances?: GalaxyResonanceCreateNestedManyWithoutGalaxyEmotionInput
  }

  export type GalaxyEmotionUncheckedCreateInput = {
    id?: string
    content: string
    emotion: $Enums.Emotion
    intensity?: number
    position: JsonNullValueInput | InputJsonValue
    color: string
    size?: number
    expiresAt: Date | string
    createdAt?: Date | string
    resonances?: GalaxyResonanceUncheckedCreateNestedManyWithoutGalaxyEmotionInput
  }

  export type GalaxyEmotionUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    intensity?: FloatFieldUpdateOperationsInput | number
    position?: JsonNullValueInput | InputJsonValue
    color?: StringFieldUpdateOperationsInput | string
    size?: FloatFieldUpdateOperationsInput | number
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resonances?: GalaxyResonanceUpdateManyWithoutGalaxyEmotionNestedInput
  }

  export type GalaxyEmotionUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    intensity?: FloatFieldUpdateOperationsInput | number
    position?: JsonNullValueInput | InputJsonValue
    color?: StringFieldUpdateOperationsInput | string
    size?: FloatFieldUpdateOperationsInput | number
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resonances?: GalaxyResonanceUncheckedUpdateManyWithoutGalaxyEmotionNestedInput
  }

  export type GalaxyEmotionCreateManyInput = {
    id?: string
    content: string
    emotion: $Enums.Emotion
    intensity?: number
    position: JsonNullValueInput | InputJsonValue
    color: string
    size?: number
    expiresAt: Date | string
    createdAt?: Date | string
  }

  export type GalaxyEmotionUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    intensity?: FloatFieldUpdateOperationsInput | number
    position?: JsonNullValueInput | InputJsonValue
    color?: StringFieldUpdateOperationsInput | string
    size?: FloatFieldUpdateOperationsInput | number
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyEmotionUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    intensity?: FloatFieldUpdateOperationsInput | number
    position?: JsonNullValueInput | InputJsonValue
    color?: StringFieldUpdateOperationsInput | string
    size?: FloatFieldUpdateOperationsInput | number
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyResonanceCreateInput = {
    id?: string
    userId: string
    type?: $Enums.ResonanceType
    createdAt?: Date | string
    galaxyEmotion: GalaxyEmotionCreateNestedOneWithoutResonancesInput
  }

  export type GalaxyResonanceUncheckedCreateInput = {
    id?: string
    galaxyEmotionId: string
    userId: string
    type?: $Enums.ResonanceType
    createdAt?: Date | string
  }

  export type GalaxyResonanceUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    galaxyEmotion?: GalaxyEmotionUpdateOneRequiredWithoutResonancesNestedInput
  }

  export type GalaxyResonanceUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    galaxyEmotionId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyResonanceCreateManyInput = {
    id?: string
    galaxyEmotionId: string
    userId: string
    type?: $Enums.ResonanceType
    createdAt?: Date | string
  }

  export type GalaxyResonanceUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyResonanceUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    galaxyEmotionId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type StarListRelationFilter = {
    every?: StarWhereInput
    some?: StarWhereInput
    none?: StarWhereInput
  }

  export type ResonanceListRelationFilter = {
    every?: ResonanceWhereInput
    some?: ResonanceWhereInput
    none?: ResonanceWhereInput
  }

  export type SessionListRelationFilter = {
    every?: SessionWhereInput
    some?: SessionWhereInput
    none?: SessionWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type StarOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type ResonanceOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type SessionOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    avatar?: SortOrder
    bio?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    avatar?: SortOrder
    bio?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    avatar?: SortOrder
    bio?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type SessionCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    token?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type SessionMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    token?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type SessionMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    token?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type EnumStarTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.StarType | EnumStarTypeFieldRefInput<$PrismaModel>
    in?: $Enums.StarType[]
    notIn?: $Enums.StarType[]
    not?: NestedEnumStarTypeFilter<$PrismaModel> | $Enums.StarType
  }

  export type EnumEmotionFilter<$PrismaModel = never> = {
    equals?: $Enums.Emotion | EnumEmotionFieldRefInput<$PrismaModel>
    in?: $Enums.Emotion[]
    notIn?: $Enums.Emotion[]
    not?: NestedEnumEmotionFilter<$PrismaModel> | $Enums.Emotion
  }

  export type FloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type StarCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    content?: SortOrder
    type?: SortOrder
    mediaUrl?: SortOrder
    emotion?: SortOrder
    brightness?: SortOrder
    isSealed?: SortOrder
    sealedUntil?: SortOrder
    position?: SortOrder
    color?: SortOrder
    size?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StarAvgOrderByAggregateInput = {
    brightness?: SortOrder
    size?: SortOrder
  }

  export type StarMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    content?: SortOrder
    type?: SortOrder
    mediaUrl?: SortOrder
    emotion?: SortOrder
    brightness?: SortOrder
    isSealed?: SortOrder
    sealedUntil?: SortOrder
    color?: SortOrder
    size?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StarMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    content?: SortOrder
    type?: SortOrder
    mediaUrl?: SortOrder
    emotion?: SortOrder
    brightness?: SortOrder
    isSealed?: SortOrder
    sealedUntil?: SortOrder
    color?: SortOrder
    size?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StarSumOrderByAggregateInput = {
    brightness?: SortOrder
    size?: SortOrder
  }

  export type EnumStarTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.StarType | EnumStarTypeFieldRefInput<$PrismaModel>
    in?: $Enums.StarType[]
    notIn?: $Enums.StarType[]
    not?: NestedEnumStarTypeWithAggregatesFilter<$PrismaModel> | $Enums.StarType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumStarTypeFilter<$PrismaModel>
    _max?: NestedEnumStarTypeFilter<$PrismaModel>
  }

  export type EnumEmotionWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.Emotion | EnumEmotionFieldRefInput<$PrismaModel>
    in?: $Enums.Emotion[]
    notIn?: $Enums.Emotion[]
    not?: NestedEnumEmotionWithAggregatesFilter<$PrismaModel> | $Enums.Emotion
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumEmotionFilter<$PrismaModel>
    _max?: NestedEnumEmotionFilter<$PrismaModel>
  }

  export type FloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type EnumResonanceTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.ResonanceType | EnumResonanceTypeFieldRefInput<$PrismaModel>
    in?: $Enums.ResonanceType[]
    notIn?: $Enums.ResonanceType[]
    not?: NestedEnumResonanceTypeFilter<$PrismaModel> | $Enums.ResonanceType
  }

  export type StarScalarRelationFilter = {
    is?: StarWhereInput
    isNot?: StarWhereInput
  }

  export type ResonanceStarIdUserIdCompoundUniqueInput = {
    starId: string
    userId: string
  }

  export type ResonanceCountOrderByAggregateInput = {
    id?: SortOrder
    starId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    message?: SortOrder
    isAnonymous?: SortOrder
    createdAt?: SortOrder
  }

  export type ResonanceMaxOrderByAggregateInput = {
    id?: SortOrder
    starId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    message?: SortOrder
    isAnonymous?: SortOrder
    createdAt?: SortOrder
  }

  export type ResonanceMinOrderByAggregateInput = {
    id?: SortOrder
    starId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    message?: SortOrder
    isAnonymous?: SortOrder
    createdAt?: SortOrder
  }

  export type EnumResonanceTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.ResonanceType | EnumResonanceTypeFieldRefInput<$PrismaModel>
    in?: $Enums.ResonanceType[]
    notIn?: $Enums.ResonanceType[]
    not?: NestedEnumResonanceTypeWithAggregatesFilter<$PrismaModel> | $Enums.ResonanceType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumResonanceTypeFilter<$PrismaModel>
    _max?: NestedEnumResonanceTypeFilter<$PrismaModel>
  }
  export type JsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type GalaxyResonanceListRelationFilter = {
    every?: GalaxyResonanceWhereInput
    some?: GalaxyResonanceWhereInput
    none?: GalaxyResonanceWhereInput
  }

  export type GalaxyResonanceOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type GalaxyEmotionCountOrderByAggregateInput = {
    id?: SortOrder
    content?: SortOrder
    emotion?: SortOrder
    intensity?: SortOrder
    position?: SortOrder
    color?: SortOrder
    size?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type GalaxyEmotionAvgOrderByAggregateInput = {
    intensity?: SortOrder
    size?: SortOrder
  }

  export type GalaxyEmotionMaxOrderByAggregateInput = {
    id?: SortOrder
    content?: SortOrder
    emotion?: SortOrder
    intensity?: SortOrder
    color?: SortOrder
    size?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type GalaxyEmotionMinOrderByAggregateInput = {
    id?: SortOrder
    content?: SortOrder
    emotion?: SortOrder
    intensity?: SortOrder
    color?: SortOrder
    size?: SortOrder
    expiresAt?: SortOrder
    createdAt?: SortOrder
  }

  export type GalaxyEmotionSumOrderByAggregateInput = {
    intensity?: SortOrder
    size?: SortOrder
  }
  export type JsonWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedJsonFilter<$PrismaModel>
    _max?: NestedJsonFilter<$PrismaModel>
  }

  export type GalaxyEmotionScalarRelationFilter = {
    is?: GalaxyEmotionWhereInput
    isNot?: GalaxyEmotionWhereInput
  }

  export type GalaxyResonanceGalaxyEmotionIdUserIdCompoundUniqueInput = {
    galaxyEmotionId: string
    userId: string
  }

  export type GalaxyResonanceCountOrderByAggregateInput = {
    id?: SortOrder
    galaxyEmotionId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
  }

  export type GalaxyResonanceMaxOrderByAggregateInput = {
    id?: SortOrder
    galaxyEmotionId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
  }

  export type GalaxyResonanceMinOrderByAggregateInput = {
    id?: SortOrder
    galaxyEmotionId?: SortOrder
    userId?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
  }

  export type StarCreateNestedManyWithoutUserInput = {
    create?: XOR<StarCreateWithoutUserInput, StarUncheckedCreateWithoutUserInput> | StarCreateWithoutUserInput[] | StarUncheckedCreateWithoutUserInput[]
    connectOrCreate?: StarCreateOrConnectWithoutUserInput | StarCreateOrConnectWithoutUserInput[]
    createMany?: StarCreateManyUserInputEnvelope
    connect?: StarWhereUniqueInput | StarWhereUniqueInput[]
  }

  export type ResonanceCreateNestedManyWithoutUserInput = {
    create?: XOR<ResonanceCreateWithoutUserInput, ResonanceUncheckedCreateWithoutUserInput> | ResonanceCreateWithoutUserInput[] | ResonanceUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ResonanceCreateOrConnectWithoutUserInput | ResonanceCreateOrConnectWithoutUserInput[]
    createMany?: ResonanceCreateManyUserInputEnvelope
    connect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
  }

  export type SessionCreateNestedManyWithoutUserInput = {
    create?: XOR<SessionCreateWithoutUserInput, SessionUncheckedCreateWithoutUserInput> | SessionCreateWithoutUserInput[] | SessionUncheckedCreateWithoutUserInput[]
    connectOrCreate?: SessionCreateOrConnectWithoutUserInput | SessionCreateOrConnectWithoutUserInput[]
    createMany?: SessionCreateManyUserInputEnvelope
    connect?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
  }

  export type StarUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<StarCreateWithoutUserInput, StarUncheckedCreateWithoutUserInput> | StarCreateWithoutUserInput[] | StarUncheckedCreateWithoutUserInput[]
    connectOrCreate?: StarCreateOrConnectWithoutUserInput | StarCreateOrConnectWithoutUserInput[]
    createMany?: StarCreateManyUserInputEnvelope
    connect?: StarWhereUniqueInput | StarWhereUniqueInput[]
  }

  export type ResonanceUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<ResonanceCreateWithoutUserInput, ResonanceUncheckedCreateWithoutUserInput> | ResonanceCreateWithoutUserInput[] | ResonanceUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ResonanceCreateOrConnectWithoutUserInput | ResonanceCreateOrConnectWithoutUserInput[]
    createMany?: ResonanceCreateManyUserInputEnvelope
    connect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
  }

  export type SessionUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<SessionCreateWithoutUserInput, SessionUncheckedCreateWithoutUserInput> | SessionCreateWithoutUserInput[] | SessionUncheckedCreateWithoutUserInput[]
    connectOrCreate?: SessionCreateOrConnectWithoutUserInput | SessionCreateOrConnectWithoutUserInput[]
    createMany?: SessionCreateManyUserInputEnvelope
    connect?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type StarUpdateManyWithoutUserNestedInput = {
    create?: XOR<StarCreateWithoutUserInput, StarUncheckedCreateWithoutUserInput> | StarCreateWithoutUserInput[] | StarUncheckedCreateWithoutUserInput[]
    connectOrCreate?: StarCreateOrConnectWithoutUserInput | StarCreateOrConnectWithoutUserInput[]
    upsert?: StarUpsertWithWhereUniqueWithoutUserInput | StarUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: StarCreateManyUserInputEnvelope
    set?: StarWhereUniqueInput | StarWhereUniqueInput[]
    disconnect?: StarWhereUniqueInput | StarWhereUniqueInput[]
    delete?: StarWhereUniqueInput | StarWhereUniqueInput[]
    connect?: StarWhereUniqueInput | StarWhereUniqueInput[]
    update?: StarUpdateWithWhereUniqueWithoutUserInput | StarUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: StarUpdateManyWithWhereWithoutUserInput | StarUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: StarScalarWhereInput | StarScalarWhereInput[]
  }

  export type ResonanceUpdateManyWithoutUserNestedInput = {
    create?: XOR<ResonanceCreateWithoutUserInput, ResonanceUncheckedCreateWithoutUserInput> | ResonanceCreateWithoutUserInput[] | ResonanceUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ResonanceCreateOrConnectWithoutUserInput | ResonanceCreateOrConnectWithoutUserInput[]
    upsert?: ResonanceUpsertWithWhereUniqueWithoutUserInput | ResonanceUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: ResonanceCreateManyUserInputEnvelope
    set?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    disconnect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    delete?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    connect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    update?: ResonanceUpdateWithWhereUniqueWithoutUserInput | ResonanceUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: ResonanceUpdateManyWithWhereWithoutUserInput | ResonanceUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: ResonanceScalarWhereInput | ResonanceScalarWhereInput[]
  }

  export type SessionUpdateManyWithoutUserNestedInput = {
    create?: XOR<SessionCreateWithoutUserInput, SessionUncheckedCreateWithoutUserInput> | SessionCreateWithoutUserInput[] | SessionUncheckedCreateWithoutUserInput[]
    connectOrCreate?: SessionCreateOrConnectWithoutUserInput | SessionCreateOrConnectWithoutUserInput[]
    upsert?: SessionUpsertWithWhereUniqueWithoutUserInput | SessionUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: SessionCreateManyUserInputEnvelope
    set?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
    disconnect?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
    delete?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
    connect?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
    update?: SessionUpdateWithWhereUniqueWithoutUserInput | SessionUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: SessionUpdateManyWithWhereWithoutUserInput | SessionUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: SessionScalarWhereInput | SessionScalarWhereInput[]
  }

  export type StarUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<StarCreateWithoutUserInput, StarUncheckedCreateWithoutUserInput> | StarCreateWithoutUserInput[] | StarUncheckedCreateWithoutUserInput[]
    connectOrCreate?: StarCreateOrConnectWithoutUserInput | StarCreateOrConnectWithoutUserInput[]
    upsert?: StarUpsertWithWhereUniqueWithoutUserInput | StarUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: StarCreateManyUserInputEnvelope
    set?: StarWhereUniqueInput | StarWhereUniqueInput[]
    disconnect?: StarWhereUniqueInput | StarWhereUniqueInput[]
    delete?: StarWhereUniqueInput | StarWhereUniqueInput[]
    connect?: StarWhereUniqueInput | StarWhereUniqueInput[]
    update?: StarUpdateWithWhereUniqueWithoutUserInput | StarUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: StarUpdateManyWithWhereWithoutUserInput | StarUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: StarScalarWhereInput | StarScalarWhereInput[]
  }

  export type ResonanceUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<ResonanceCreateWithoutUserInput, ResonanceUncheckedCreateWithoutUserInput> | ResonanceCreateWithoutUserInput[] | ResonanceUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ResonanceCreateOrConnectWithoutUserInput | ResonanceCreateOrConnectWithoutUserInput[]
    upsert?: ResonanceUpsertWithWhereUniqueWithoutUserInput | ResonanceUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: ResonanceCreateManyUserInputEnvelope
    set?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    disconnect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    delete?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    connect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    update?: ResonanceUpdateWithWhereUniqueWithoutUserInput | ResonanceUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: ResonanceUpdateManyWithWhereWithoutUserInput | ResonanceUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: ResonanceScalarWhereInput | ResonanceScalarWhereInput[]
  }

  export type SessionUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<SessionCreateWithoutUserInput, SessionUncheckedCreateWithoutUserInput> | SessionCreateWithoutUserInput[] | SessionUncheckedCreateWithoutUserInput[]
    connectOrCreate?: SessionCreateOrConnectWithoutUserInput | SessionCreateOrConnectWithoutUserInput[]
    upsert?: SessionUpsertWithWhereUniqueWithoutUserInput | SessionUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: SessionCreateManyUserInputEnvelope
    set?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
    disconnect?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
    delete?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
    connect?: SessionWhereUniqueInput | SessionWhereUniqueInput[]
    update?: SessionUpdateWithWhereUniqueWithoutUserInput | SessionUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: SessionUpdateManyWithWhereWithoutUserInput | SessionUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: SessionScalarWhereInput | SessionScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutSessionsInput = {
    create?: XOR<UserCreateWithoutSessionsInput, UserUncheckedCreateWithoutSessionsInput>
    connectOrCreate?: UserCreateOrConnectWithoutSessionsInput
    connect?: UserWhereUniqueInput
  }

  export type UserUpdateOneRequiredWithoutSessionsNestedInput = {
    create?: XOR<UserCreateWithoutSessionsInput, UserUncheckedCreateWithoutSessionsInput>
    connectOrCreate?: UserCreateOrConnectWithoutSessionsInput
    upsert?: UserUpsertWithoutSessionsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutSessionsInput, UserUpdateWithoutSessionsInput>, UserUncheckedUpdateWithoutSessionsInput>
  }

  export type UserCreateNestedOneWithoutStarsInput = {
    create?: XOR<UserCreateWithoutStarsInput, UserUncheckedCreateWithoutStarsInput>
    connectOrCreate?: UserCreateOrConnectWithoutStarsInput
    connect?: UserWhereUniqueInput
  }

  export type ResonanceCreateNestedManyWithoutStarInput = {
    create?: XOR<ResonanceCreateWithoutStarInput, ResonanceUncheckedCreateWithoutStarInput> | ResonanceCreateWithoutStarInput[] | ResonanceUncheckedCreateWithoutStarInput[]
    connectOrCreate?: ResonanceCreateOrConnectWithoutStarInput | ResonanceCreateOrConnectWithoutStarInput[]
    createMany?: ResonanceCreateManyStarInputEnvelope
    connect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
  }

  export type ResonanceUncheckedCreateNestedManyWithoutStarInput = {
    create?: XOR<ResonanceCreateWithoutStarInput, ResonanceUncheckedCreateWithoutStarInput> | ResonanceCreateWithoutStarInput[] | ResonanceUncheckedCreateWithoutStarInput[]
    connectOrCreate?: ResonanceCreateOrConnectWithoutStarInput | ResonanceCreateOrConnectWithoutStarInput[]
    createMany?: ResonanceCreateManyStarInputEnvelope
    connect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
  }

  export type EnumStarTypeFieldUpdateOperationsInput = {
    set?: $Enums.StarType
  }

  export type EnumEmotionFieldUpdateOperationsInput = {
    set?: $Enums.Emotion
  }

  export type FloatFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type UserUpdateOneRequiredWithoutStarsNestedInput = {
    create?: XOR<UserCreateWithoutStarsInput, UserUncheckedCreateWithoutStarsInput>
    connectOrCreate?: UserCreateOrConnectWithoutStarsInput
    upsert?: UserUpsertWithoutStarsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutStarsInput, UserUpdateWithoutStarsInput>, UserUncheckedUpdateWithoutStarsInput>
  }

  export type ResonanceUpdateManyWithoutStarNestedInput = {
    create?: XOR<ResonanceCreateWithoutStarInput, ResonanceUncheckedCreateWithoutStarInput> | ResonanceCreateWithoutStarInput[] | ResonanceUncheckedCreateWithoutStarInput[]
    connectOrCreate?: ResonanceCreateOrConnectWithoutStarInput | ResonanceCreateOrConnectWithoutStarInput[]
    upsert?: ResonanceUpsertWithWhereUniqueWithoutStarInput | ResonanceUpsertWithWhereUniqueWithoutStarInput[]
    createMany?: ResonanceCreateManyStarInputEnvelope
    set?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    disconnect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    delete?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    connect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    update?: ResonanceUpdateWithWhereUniqueWithoutStarInput | ResonanceUpdateWithWhereUniqueWithoutStarInput[]
    updateMany?: ResonanceUpdateManyWithWhereWithoutStarInput | ResonanceUpdateManyWithWhereWithoutStarInput[]
    deleteMany?: ResonanceScalarWhereInput | ResonanceScalarWhereInput[]
  }

  export type ResonanceUncheckedUpdateManyWithoutStarNestedInput = {
    create?: XOR<ResonanceCreateWithoutStarInput, ResonanceUncheckedCreateWithoutStarInput> | ResonanceCreateWithoutStarInput[] | ResonanceUncheckedCreateWithoutStarInput[]
    connectOrCreate?: ResonanceCreateOrConnectWithoutStarInput | ResonanceCreateOrConnectWithoutStarInput[]
    upsert?: ResonanceUpsertWithWhereUniqueWithoutStarInput | ResonanceUpsertWithWhereUniqueWithoutStarInput[]
    createMany?: ResonanceCreateManyStarInputEnvelope
    set?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    disconnect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    delete?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    connect?: ResonanceWhereUniqueInput | ResonanceWhereUniqueInput[]
    update?: ResonanceUpdateWithWhereUniqueWithoutStarInput | ResonanceUpdateWithWhereUniqueWithoutStarInput[]
    updateMany?: ResonanceUpdateManyWithWhereWithoutStarInput | ResonanceUpdateManyWithWhereWithoutStarInput[]
    deleteMany?: ResonanceScalarWhereInput | ResonanceScalarWhereInput[]
  }

  export type StarCreateNestedOneWithoutResonancesInput = {
    create?: XOR<StarCreateWithoutResonancesInput, StarUncheckedCreateWithoutResonancesInput>
    connectOrCreate?: StarCreateOrConnectWithoutResonancesInput
    connect?: StarWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutResonancesInput = {
    create?: XOR<UserCreateWithoutResonancesInput, UserUncheckedCreateWithoutResonancesInput>
    connectOrCreate?: UserCreateOrConnectWithoutResonancesInput
    connect?: UserWhereUniqueInput
  }

  export type EnumResonanceTypeFieldUpdateOperationsInput = {
    set?: $Enums.ResonanceType
  }

  export type StarUpdateOneRequiredWithoutResonancesNestedInput = {
    create?: XOR<StarCreateWithoutResonancesInput, StarUncheckedCreateWithoutResonancesInput>
    connectOrCreate?: StarCreateOrConnectWithoutResonancesInput
    upsert?: StarUpsertWithoutResonancesInput
    connect?: StarWhereUniqueInput
    update?: XOR<XOR<StarUpdateToOneWithWhereWithoutResonancesInput, StarUpdateWithoutResonancesInput>, StarUncheckedUpdateWithoutResonancesInput>
  }

  export type UserUpdateOneRequiredWithoutResonancesNestedInput = {
    create?: XOR<UserCreateWithoutResonancesInput, UserUncheckedCreateWithoutResonancesInput>
    connectOrCreate?: UserCreateOrConnectWithoutResonancesInput
    upsert?: UserUpsertWithoutResonancesInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutResonancesInput, UserUpdateWithoutResonancesInput>, UserUncheckedUpdateWithoutResonancesInput>
  }

  export type GalaxyResonanceCreateNestedManyWithoutGalaxyEmotionInput = {
    create?: XOR<GalaxyResonanceCreateWithoutGalaxyEmotionInput, GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput> | GalaxyResonanceCreateWithoutGalaxyEmotionInput[] | GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput[]
    connectOrCreate?: GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput | GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput[]
    createMany?: GalaxyResonanceCreateManyGalaxyEmotionInputEnvelope
    connect?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
  }

  export type GalaxyResonanceUncheckedCreateNestedManyWithoutGalaxyEmotionInput = {
    create?: XOR<GalaxyResonanceCreateWithoutGalaxyEmotionInput, GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput> | GalaxyResonanceCreateWithoutGalaxyEmotionInput[] | GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput[]
    connectOrCreate?: GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput | GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput[]
    createMany?: GalaxyResonanceCreateManyGalaxyEmotionInputEnvelope
    connect?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
  }

  export type GalaxyResonanceUpdateManyWithoutGalaxyEmotionNestedInput = {
    create?: XOR<GalaxyResonanceCreateWithoutGalaxyEmotionInput, GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput> | GalaxyResonanceCreateWithoutGalaxyEmotionInput[] | GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput[]
    connectOrCreate?: GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput | GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput[]
    upsert?: GalaxyResonanceUpsertWithWhereUniqueWithoutGalaxyEmotionInput | GalaxyResonanceUpsertWithWhereUniqueWithoutGalaxyEmotionInput[]
    createMany?: GalaxyResonanceCreateManyGalaxyEmotionInputEnvelope
    set?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
    disconnect?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
    delete?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
    connect?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
    update?: GalaxyResonanceUpdateWithWhereUniqueWithoutGalaxyEmotionInput | GalaxyResonanceUpdateWithWhereUniqueWithoutGalaxyEmotionInput[]
    updateMany?: GalaxyResonanceUpdateManyWithWhereWithoutGalaxyEmotionInput | GalaxyResonanceUpdateManyWithWhereWithoutGalaxyEmotionInput[]
    deleteMany?: GalaxyResonanceScalarWhereInput | GalaxyResonanceScalarWhereInput[]
  }

  export type GalaxyResonanceUncheckedUpdateManyWithoutGalaxyEmotionNestedInput = {
    create?: XOR<GalaxyResonanceCreateWithoutGalaxyEmotionInput, GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput> | GalaxyResonanceCreateWithoutGalaxyEmotionInput[] | GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput[]
    connectOrCreate?: GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput | GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput[]
    upsert?: GalaxyResonanceUpsertWithWhereUniqueWithoutGalaxyEmotionInput | GalaxyResonanceUpsertWithWhereUniqueWithoutGalaxyEmotionInput[]
    createMany?: GalaxyResonanceCreateManyGalaxyEmotionInputEnvelope
    set?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
    disconnect?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
    delete?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
    connect?: GalaxyResonanceWhereUniqueInput | GalaxyResonanceWhereUniqueInput[]
    update?: GalaxyResonanceUpdateWithWhereUniqueWithoutGalaxyEmotionInput | GalaxyResonanceUpdateWithWhereUniqueWithoutGalaxyEmotionInput[]
    updateMany?: GalaxyResonanceUpdateManyWithWhereWithoutGalaxyEmotionInput | GalaxyResonanceUpdateManyWithWhereWithoutGalaxyEmotionInput[]
    deleteMany?: GalaxyResonanceScalarWhereInput | GalaxyResonanceScalarWhereInput[]
  }

  export type GalaxyEmotionCreateNestedOneWithoutResonancesInput = {
    create?: XOR<GalaxyEmotionCreateWithoutResonancesInput, GalaxyEmotionUncheckedCreateWithoutResonancesInput>
    connectOrCreate?: GalaxyEmotionCreateOrConnectWithoutResonancesInput
    connect?: GalaxyEmotionWhereUniqueInput
  }

  export type GalaxyEmotionUpdateOneRequiredWithoutResonancesNestedInput = {
    create?: XOR<GalaxyEmotionCreateWithoutResonancesInput, GalaxyEmotionUncheckedCreateWithoutResonancesInput>
    connectOrCreate?: GalaxyEmotionCreateOrConnectWithoutResonancesInput
    upsert?: GalaxyEmotionUpsertWithoutResonancesInput
    connect?: GalaxyEmotionWhereUniqueInput
    update?: XOR<XOR<GalaxyEmotionUpdateToOneWithWhereWithoutResonancesInput, GalaxyEmotionUpdateWithoutResonancesInput>, GalaxyEmotionUncheckedUpdateWithoutResonancesInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedEnumStarTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.StarType | EnumStarTypeFieldRefInput<$PrismaModel>
    in?: $Enums.StarType[]
    notIn?: $Enums.StarType[]
    not?: NestedEnumStarTypeFilter<$PrismaModel> | $Enums.StarType
  }

  export type NestedEnumEmotionFilter<$PrismaModel = never> = {
    equals?: $Enums.Emotion | EnumEmotionFieldRefInput<$PrismaModel>
    in?: $Enums.Emotion[]
    notIn?: $Enums.Emotion[]
    not?: NestedEnumEmotionFilter<$PrismaModel> | $Enums.Emotion
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedEnumStarTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.StarType | EnumStarTypeFieldRefInput<$PrismaModel>
    in?: $Enums.StarType[]
    notIn?: $Enums.StarType[]
    not?: NestedEnumStarTypeWithAggregatesFilter<$PrismaModel> | $Enums.StarType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumStarTypeFilter<$PrismaModel>
    _max?: NestedEnumStarTypeFilter<$PrismaModel>
  }

  export type NestedEnumEmotionWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.Emotion | EnumEmotionFieldRefInput<$PrismaModel>
    in?: $Enums.Emotion[]
    notIn?: $Enums.Emotion[]
    not?: NestedEnumEmotionWithAggregatesFilter<$PrismaModel> | $Enums.Emotion
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumEmotionFilter<$PrismaModel>
    _max?: NestedEnumEmotionFilter<$PrismaModel>
  }

  export type NestedFloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedEnumResonanceTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.ResonanceType | EnumResonanceTypeFieldRefInput<$PrismaModel>
    in?: $Enums.ResonanceType[]
    notIn?: $Enums.ResonanceType[]
    not?: NestedEnumResonanceTypeFilter<$PrismaModel> | $Enums.ResonanceType
  }

  export type NestedEnumResonanceTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.ResonanceType | EnumResonanceTypeFieldRefInput<$PrismaModel>
    in?: $Enums.ResonanceType[]
    notIn?: $Enums.ResonanceType[]
    not?: NestedEnumResonanceTypeWithAggregatesFilter<$PrismaModel> | $Enums.ResonanceType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumResonanceTypeFilter<$PrismaModel>
    _max?: NestedEnumResonanceTypeFilter<$PrismaModel>
  }
  export type NestedJsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type StarCreateWithoutUserInput = {
    id?: string
    title?: string | null
    content: string
    type?: $Enums.StarType
    mediaUrl?: string | null
    emotion?: $Enums.Emotion
    brightness?: number
    isSealed?: boolean
    sealedUntil?: Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: string | null
    size?: number
    createdAt?: Date | string
    updatedAt?: Date | string
    resonances?: ResonanceCreateNestedManyWithoutStarInput
  }

  export type StarUncheckedCreateWithoutUserInput = {
    id?: string
    title?: string | null
    content: string
    type?: $Enums.StarType
    mediaUrl?: string | null
    emotion?: $Enums.Emotion
    brightness?: number
    isSealed?: boolean
    sealedUntil?: Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: string | null
    size?: number
    createdAt?: Date | string
    updatedAt?: Date | string
    resonances?: ResonanceUncheckedCreateNestedManyWithoutStarInput
  }

  export type StarCreateOrConnectWithoutUserInput = {
    where: StarWhereUniqueInput
    create: XOR<StarCreateWithoutUserInput, StarUncheckedCreateWithoutUserInput>
  }

  export type StarCreateManyUserInputEnvelope = {
    data: StarCreateManyUserInput | StarCreateManyUserInput[]
  }

  export type ResonanceCreateWithoutUserInput = {
    id?: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
    star: StarCreateNestedOneWithoutResonancesInput
  }

  export type ResonanceUncheckedCreateWithoutUserInput = {
    id?: string
    starId: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
  }

  export type ResonanceCreateOrConnectWithoutUserInput = {
    where: ResonanceWhereUniqueInput
    create: XOR<ResonanceCreateWithoutUserInput, ResonanceUncheckedCreateWithoutUserInput>
  }

  export type ResonanceCreateManyUserInputEnvelope = {
    data: ResonanceCreateManyUserInput | ResonanceCreateManyUserInput[]
  }

  export type SessionCreateWithoutUserInput = {
    id?: string
    token: string
    expiresAt: Date | string
    createdAt?: Date | string
  }

  export type SessionUncheckedCreateWithoutUserInput = {
    id?: string
    token: string
    expiresAt: Date | string
    createdAt?: Date | string
  }

  export type SessionCreateOrConnectWithoutUserInput = {
    where: SessionWhereUniqueInput
    create: XOR<SessionCreateWithoutUserInput, SessionUncheckedCreateWithoutUserInput>
  }

  export type SessionCreateManyUserInputEnvelope = {
    data: SessionCreateManyUserInput | SessionCreateManyUserInput[]
  }

  export type StarUpsertWithWhereUniqueWithoutUserInput = {
    where: StarWhereUniqueInput
    update: XOR<StarUpdateWithoutUserInput, StarUncheckedUpdateWithoutUserInput>
    create: XOR<StarCreateWithoutUserInput, StarUncheckedCreateWithoutUserInput>
  }

  export type StarUpdateWithWhereUniqueWithoutUserInput = {
    where: StarWhereUniqueInput
    data: XOR<StarUpdateWithoutUserInput, StarUncheckedUpdateWithoutUserInput>
  }

  export type StarUpdateManyWithWhereWithoutUserInput = {
    where: StarScalarWhereInput
    data: XOR<StarUpdateManyMutationInput, StarUncheckedUpdateManyWithoutUserInput>
  }

  export type StarScalarWhereInput = {
    AND?: StarScalarWhereInput | StarScalarWhereInput[]
    OR?: StarScalarWhereInput[]
    NOT?: StarScalarWhereInput | StarScalarWhereInput[]
    id?: StringFilter<"Star"> | string
    userId?: StringFilter<"Star"> | string
    title?: StringNullableFilter<"Star"> | string | null
    content?: StringFilter<"Star"> | string
    type?: EnumStarTypeFilter<"Star"> | $Enums.StarType
    mediaUrl?: StringNullableFilter<"Star"> | string | null
    emotion?: EnumEmotionFilter<"Star"> | $Enums.Emotion
    brightness?: FloatFilter<"Star"> | number
    isSealed?: BoolFilter<"Star"> | boolean
    sealedUntil?: DateTimeNullableFilter<"Star"> | Date | string | null
    position?: JsonNullableFilter<"Star">
    color?: StringNullableFilter<"Star"> | string | null
    size?: FloatFilter<"Star"> | number
    createdAt?: DateTimeFilter<"Star"> | Date | string
    updatedAt?: DateTimeFilter<"Star"> | Date | string
  }

  export type ResonanceUpsertWithWhereUniqueWithoutUserInput = {
    where: ResonanceWhereUniqueInput
    update: XOR<ResonanceUpdateWithoutUserInput, ResonanceUncheckedUpdateWithoutUserInput>
    create: XOR<ResonanceCreateWithoutUserInput, ResonanceUncheckedCreateWithoutUserInput>
  }

  export type ResonanceUpdateWithWhereUniqueWithoutUserInput = {
    where: ResonanceWhereUniqueInput
    data: XOR<ResonanceUpdateWithoutUserInput, ResonanceUncheckedUpdateWithoutUserInput>
  }

  export type ResonanceUpdateManyWithWhereWithoutUserInput = {
    where: ResonanceScalarWhereInput
    data: XOR<ResonanceUpdateManyMutationInput, ResonanceUncheckedUpdateManyWithoutUserInput>
  }

  export type ResonanceScalarWhereInput = {
    AND?: ResonanceScalarWhereInput | ResonanceScalarWhereInput[]
    OR?: ResonanceScalarWhereInput[]
    NOT?: ResonanceScalarWhereInput | ResonanceScalarWhereInput[]
    id?: StringFilter<"Resonance"> | string
    starId?: StringFilter<"Resonance"> | string
    userId?: StringFilter<"Resonance"> | string
    type?: EnumResonanceTypeFilter<"Resonance"> | $Enums.ResonanceType
    message?: StringNullableFilter<"Resonance"> | string | null
    isAnonymous?: BoolFilter<"Resonance"> | boolean
    createdAt?: DateTimeFilter<"Resonance"> | Date | string
  }

  export type SessionUpsertWithWhereUniqueWithoutUserInput = {
    where: SessionWhereUniqueInput
    update: XOR<SessionUpdateWithoutUserInput, SessionUncheckedUpdateWithoutUserInput>
    create: XOR<SessionCreateWithoutUserInput, SessionUncheckedCreateWithoutUserInput>
  }

  export type SessionUpdateWithWhereUniqueWithoutUserInput = {
    where: SessionWhereUniqueInput
    data: XOR<SessionUpdateWithoutUserInput, SessionUncheckedUpdateWithoutUserInput>
  }

  export type SessionUpdateManyWithWhereWithoutUserInput = {
    where: SessionScalarWhereInput
    data: XOR<SessionUpdateManyMutationInput, SessionUncheckedUpdateManyWithoutUserInput>
  }

  export type SessionScalarWhereInput = {
    AND?: SessionScalarWhereInput | SessionScalarWhereInput[]
    OR?: SessionScalarWhereInput[]
    NOT?: SessionScalarWhereInput | SessionScalarWhereInput[]
    id?: StringFilter<"Session"> | string
    userId?: StringFilter<"Session"> | string
    token?: StringFilter<"Session"> | string
    expiresAt?: DateTimeFilter<"Session"> | Date | string
    createdAt?: DateTimeFilter<"Session"> | Date | string
  }

  export type UserCreateWithoutSessionsInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    stars?: StarCreateNestedManyWithoutUserInput
    resonances?: ResonanceCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutSessionsInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    stars?: StarUncheckedCreateNestedManyWithoutUserInput
    resonances?: ResonanceUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutSessionsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutSessionsInput, UserUncheckedCreateWithoutSessionsInput>
  }

  export type UserUpsertWithoutSessionsInput = {
    update: XOR<UserUpdateWithoutSessionsInput, UserUncheckedUpdateWithoutSessionsInput>
    create: XOR<UserCreateWithoutSessionsInput, UserUncheckedCreateWithoutSessionsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutSessionsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutSessionsInput, UserUncheckedUpdateWithoutSessionsInput>
  }

  export type UserUpdateWithoutSessionsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    stars?: StarUpdateManyWithoutUserNestedInput
    resonances?: ResonanceUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutSessionsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    stars?: StarUncheckedUpdateManyWithoutUserNestedInput
    resonances?: ResonanceUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateWithoutStarsInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    resonances?: ResonanceCreateNestedManyWithoutUserInput
    sessions?: SessionCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutStarsInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    resonances?: ResonanceUncheckedCreateNestedManyWithoutUserInput
    sessions?: SessionUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutStarsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutStarsInput, UserUncheckedCreateWithoutStarsInput>
  }

  export type ResonanceCreateWithoutStarInput = {
    id?: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutResonancesInput
  }

  export type ResonanceUncheckedCreateWithoutStarInput = {
    id?: string
    userId: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
  }

  export type ResonanceCreateOrConnectWithoutStarInput = {
    where: ResonanceWhereUniqueInput
    create: XOR<ResonanceCreateWithoutStarInput, ResonanceUncheckedCreateWithoutStarInput>
  }

  export type ResonanceCreateManyStarInputEnvelope = {
    data: ResonanceCreateManyStarInput | ResonanceCreateManyStarInput[]
  }

  export type UserUpsertWithoutStarsInput = {
    update: XOR<UserUpdateWithoutStarsInput, UserUncheckedUpdateWithoutStarsInput>
    create: XOR<UserCreateWithoutStarsInput, UserUncheckedCreateWithoutStarsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutStarsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutStarsInput, UserUncheckedUpdateWithoutStarsInput>
  }

  export type UserUpdateWithoutStarsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resonances?: ResonanceUpdateManyWithoutUserNestedInput
    sessions?: SessionUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutStarsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resonances?: ResonanceUncheckedUpdateManyWithoutUserNestedInput
    sessions?: SessionUncheckedUpdateManyWithoutUserNestedInput
  }

  export type ResonanceUpsertWithWhereUniqueWithoutStarInput = {
    where: ResonanceWhereUniqueInput
    update: XOR<ResonanceUpdateWithoutStarInput, ResonanceUncheckedUpdateWithoutStarInput>
    create: XOR<ResonanceCreateWithoutStarInput, ResonanceUncheckedCreateWithoutStarInput>
  }

  export type ResonanceUpdateWithWhereUniqueWithoutStarInput = {
    where: ResonanceWhereUniqueInput
    data: XOR<ResonanceUpdateWithoutStarInput, ResonanceUncheckedUpdateWithoutStarInput>
  }

  export type ResonanceUpdateManyWithWhereWithoutStarInput = {
    where: ResonanceScalarWhereInput
    data: XOR<ResonanceUpdateManyMutationInput, ResonanceUncheckedUpdateManyWithoutStarInput>
  }

  export type StarCreateWithoutResonancesInput = {
    id?: string
    title?: string | null
    content: string
    type?: $Enums.StarType
    mediaUrl?: string | null
    emotion?: $Enums.Emotion
    brightness?: number
    isSealed?: boolean
    sealedUntil?: Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: string | null
    size?: number
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutStarsInput
  }

  export type StarUncheckedCreateWithoutResonancesInput = {
    id?: string
    userId: string
    title?: string | null
    content: string
    type?: $Enums.StarType
    mediaUrl?: string | null
    emotion?: $Enums.Emotion
    brightness?: number
    isSealed?: boolean
    sealedUntil?: Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: string | null
    size?: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StarCreateOrConnectWithoutResonancesInput = {
    where: StarWhereUniqueInput
    create: XOR<StarCreateWithoutResonancesInput, StarUncheckedCreateWithoutResonancesInput>
  }

  export type UserCreateWithoutResonancesInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    stars?: StarCreateNestedManyWithoutUserInput
    sessions?: SessionCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutResonancesInput = {
    id?: string
    email: string
    username: string
    password: string
    avatar?: string | null
    bio?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    stars?: StarUncheckedCreateNestedManyWithoutUserInput
    sessions?: SessionUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutResonancesInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutResonancesInput, UserUncheckedCreateWithoutResonancesInput>
  }

  export type StarUpsertWithoutResonancesInput = {
    update: XOR<StarUpdateWithoutResonancesInput, StarUncheckedUpdateWithoutResonancesInput>
    create: XOR<StarCreateWithoutResonancesInput, StarUncheckedCreateWithoutResonancesInput>
    where?: StarWhereInput
  }

  export type StarUpdateToOneWithWhereWithoutResonancesInput = {
    where?: StarWhereInput
    data: XOR<StarUpdateWithoutResonancesInput, StarUncheckedUpdateWithoutResonancesInput>
  }

  export type StarUpdateWithoutResonancesInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutStarsNestedInput
  }

  export type StarUncheckedUpdateWithoutResonancesInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUpsertWithoutResonancesInput = {
    update: XOR<UserUpdateWithoutResonancesInput, UserUncheckedUpdateWithoutResonancesInput>
    create: XOR<UserCreateWithoutResonancesInput, UserUncheckedCreateWithoutResonancesInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutResonancesInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutResonancesInput, UserUncheckedUpdateWithoutResonancesInput>
  }

  export type UserUpdateWithoutResonancesInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    stars?: StarUpdateManyWithoutUserNestedInput
    sessions?: SessionUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutResonancesInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    stars?: StarUncheckedUpdateManyWithoutUserNestedInput
    sessions?: SessionUncheckedUpdateManyWithoutUserNestedInput
  }

  export type GalaxyResonanceCreateWithoutGalaxyEmotionInput = {
    id?: string
    userId: string
    type?: $Enums.ResonanceType
    createdAt?: Date | string
  }

  export type GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput = {
    id?: string
    userId: string
    type?: $Enums.ResonanceType
    createdAt?: Date | string
  }

  export type GalaxyResonanceCreateOrConnectWithoutGalaxyEmotionInput = {
    where: GalaxyResonanceWhereUniqueInput
    create: XOR<GalaxyResonanceCreateWithoutGalaxyEmotionInput, GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput>
  }

  export type GalaxyResonanceCreateManyGalaxyEmotionInputEnvelope = {
    data: GalaxyResonanceCreateManyGalaxyEmotionInput | GalaxyResonanceCreateManyGalaxyEmotionInput[]
  }

  export type GalaxyResonanceUpsertWithWhereUniqueWithoutGalaxyEmotionInput = {
    where: GalaxyResonanceWhereUniqueInput
    update: XOR<GalaxyResonanceUpdateWithoutGalaxyEmotionInput, GalaxyResonanceUncheckedUpdateWithoutGalaxyEmotionInput>
    create: XOR<GalaxyResonanceCreateWithoutGalaxyEmotionInput, GalaxyResonanceUncheckedCreateWithoutGalaxyEmotionInput>
  }

  export type GalaxyResonanceUpdateWithWhereUniqueWithoutGalaxyEmotionInput = {
    where: GalaxyResonanceWhereUniqueInput
    data: XOR<GalaxyResonanceUpdateWithoutGalaxyEmotionInput, GalaxyResonanceUncheckedUpdateWithoutGalaxyEmotionInput>
  }

  export type GalaxyResonanceUpdateManyWithWhereWithoutGalaxyEmotionInput = {
    where: GalaxyResonanceScalarWhereInput
    data: XOR<GalaxyResonanceUpdateManyMutationInput, GalaxyResonanceUncheckedUpdateManyWithoutGalaxyEmotionInput>
  }

  export type GalaxyResonanceScalarWhereInput = {
    AND?: GalaxyResonanceScalarWhereInput | GalaxyResonanceScalarWhereInput[]
    OR?: GalaxyResonanceScalarWhereInput[]
    NOT?: GalaxyResonanceScalarWhereInput | GalaxyResonanceScalarWhereInput[]
    id?: StringFilter<"GalaxyResonance"> | string
    galaxyEmotionId?: StringFilter<"GalaxyResonance"> | string
    userId?: StringFilter<"GalaxyResonance"> | string
    type?: EnumResonanceTypeFilter<"GalaxyResonance"> | $Enums.ResonanceType
    createdAt?: DateTimeFilter<"GalaxyResonance"> | Date | string
  }

  export type GalaxyEmotionCreateWithoutResonancesInput = {
    id?: string
    content: string
    emotion: $Enums.Emotion
    intensity?: number
    position: JsonNullValueInput | InputJsonValue
    color: string
    size?: number
    expiresAt: Date | string
    createdAt?: Date | string
  }

  export type GalaxyEmotionUncheckedCreateWithoutResonancesInput = {
    id?: string
    content: string
    emotion: $Enums.Emotion
    intensity?: number
    position: JsonNullValueInput | InputJsonValue
    color: string
    size?: number
    expiresAt: Date | string
    createdAt?: Date | string
  }

  export type GalaxyEmotionCreateOrConnectWithoutResonancesInput = {
    where: GalaxyEmotionWhereUniqueInput
    create: XOR<GalaxyEmotionCreateWithoutResonancesInput, GalaxyEmotionUncheckedCreateWithoutResonancesInput>
  }

  export type GalaxyEmotionUpsertWithoutResonancesInput = {
    update: XOR<GalaxyEmotionUpdateWithoutResonancesInput, GalaxyEmotionUncheckedUpdateWithoutResonancesInput>
    create: XOR<GalaxyEmotionCreateWithoutResonancesInput, GalaxyEmotionUncheckedCreateWithoutResonancesInput>
    where?: GalaxyEmotionWhereInput
  }

  export type GalaxyEmotionUpdateToOneWithWhereWithoutResonancesInput = {
    where?: GalaxyEmotionWhereInput
    data: XOR<GalaxyEmotionUpdateWithoutResonancesInput, GalaxyEmotionUncheckedUpdateWithoutResonancesInput>
  }

  export type GalaxyEmotionUpdateWithoutResonancesInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    intensity?: FloatFieldUpdateOperationsInput | number
    position?: JsonNullValueInput | InputJsonValue
    color?: StringFieldUpdateOperationsInput | string
    size?: FloatFieldUpdateOperationsInput | number
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyEmotionUncheckedUpdateWithoutResonancesInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    intensity?: FloatFieldUpdateOperationsInput | number
    position?: JsonNullValueInput | InputJsonValue
    color?: StringFieldUpdateOperationsInput | string
    size?: FloatFieldUpdateOperationsInput | number
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StarCreateManyUserInput = {
    id?: string
    title?: string | null
    content: string
    type?: $Enums.StarType
    mediaUrl?: string | null
    emotion?: $Enums.Emotion
    brightness?: number
    isSealed?: boolean
    sealedUntil?: Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: string | null
    size?: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ResonanceCreateManyUserInput = {
    id?: string
    starId: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
  }

  export type SessionCreateManyUserInput = {
    id?: string
    token: string
    expiresAt: Date | string
    createdAt?: Date | string
  }

  export type StarUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resonances?: ResonanceUpdateManyWithoutStarNestedInput
  }

  export type StarUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resonances?: ResonanceUncheckedUpdateManyWithoutStarNestedInput
  }

  export type StarUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: NullableStringFieldUpdateOperationsInput | string | null
    content?: StringFieldUpdateOperationsInput | string
    type?: EnumStarTypeFieldUpdateOperationsInput | $Enums.StarType
    mediaUrl?: NullableStringFieldUpdateOperationsInput | string | null
    emotion?: EnumEmotionFieldUpdateOperationsInput | $Enums.Emotion
    brightness?: FloatFieldUpdateOperationsInput | number
    isSealed?: BoolFieldUpdateOperationsInput | boolean
    sealedUntil?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    position?: NullableJsonNullValueInput | InputJsonValue
    color?: NullableStringFieldUpdateOperationsInput | string | null
    size?: FloatFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ResonanceUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    star?: StarUpdateOneRequiredWithoutResonancesNestedInput
  }

  export type ResonanceUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    starId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ResonanceUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    starId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    token?: StringFieldUpdateOperationsInput | string
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    token?: StringFieldUpdateOperationsInput | string
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    token?: StringFieldUpdateOperationsInput | string
    expiresAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ResonanceCreateManyStarInput = {
    id?: string
    userId: string
    type?: $Enums.ResonanceType
    message?: string | null
    isAnonymous?: boolean
    createdAt?: Date | string
  }

  export type ResonanceUpdateWithoutStarInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutResonancesNestedInput
  }

  export type ResonanceUncheckedUpdateWithoutStarInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ResonanceUncheckedUpdateManyWithoutStarInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    message?: NullableStringFieldUpdateOperationsInput | string | null
    isAnonymous?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyResonanceCreateManyGalaxyEmotionInput = {
    id?: string
    userId: string
    type?: $Enums.ResonanceType
    createdAt?: Date | string
  }

  export type GalaxyResonanceUpdateWithoutGalaxyEmotionInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyResonanceUncheckedUpdateWithoutGalaxyEmotionInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GalaxyResonanceUncheckedUpdateManyWithoutGalaxyEmotionInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    type?: EnumResonanceTypeFieldUpdateOperationsInput | $Enums.ResonanceType
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}