<template>
  <div class="starfield fixed inset-0 z-0">
    <div
      v-for="star in stars"
      :key="star.id"
      class="star absolute rounded-full"
      :style="{
        left: star.x + '%',
        top: star.y + '%',
        width: star.size + 'px',
        height: star.size + 'px',
        animationDelay: star.delay + 's',
        animationDuration: star.duration + 's'
      }"
    />
    
    <!-- 流星效果 -->
    <div
      v-for="meteor in meteors"
      :key="meteor.id"
      class="meteor absolute"
      :style="{
        left: meteor.x + '%',
        top: meteor.y + '%',
        animationDelay: meteor.delay + 's'
      }"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const stars = ref([])
const meteors = ref([])

// 生成随机星星
const generateStars = () => {
  const starCount = 150
  const newStars = []
  
  for (let i = 0; i < starCount; i++) {
    newStars.push({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      delay: Math.random() * 3,
      duration: Math.random() * 2 + 2
    })
  }
  
  stars.value = newStars
}

// 生成流星
const generateMeteors = () => {
  const meteorCount = 3
  const newMeteors = []
  
  for (let i = 0; i < meteorCount; i++) {
    newMeteors.push({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 50,
      delay: Math.random() * 10 + 5
    })
  }
  
  meteors.value = newMeteors
}

onMounted(() => {
  generateStars()
  generateMeteors()
})
</script>

<style scoped>
.starfield {
  background: linear-gradient(
    135deg,
    #050508 0%,
    #0a0a0f 30%,
    #1a0a2e 70%,
    #2d1b69 100%
  );
}

.star {
  background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(255,255,255,0.8) 50%, transparent 100%);
  animation: twinkle 3s ease-in-out infinite;
}

.meteor {
  width: 2px;
  height: 2px;
  background: linear-gradient(45deg, #00f5ff, transparent);
  border-radius: 50%;
  animation: meteor 8s linear infinite;
}

.meteor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100px;
  height: 1px;
  background: linear-gradient(90deg, #00f5ff, transparent);
  transform: rotate(45deg);
  transform-origin: 0 0;
}

@keyframes twinkle {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1);
    filter: brightness(1);
  }
  50% { 
    opacity: 1; 
    transform: scale(1.2);
    filter: brightness(1.5);
  }
}

@keyframes meteor {
  0% {
    transform: translateX(-100px) translateY(-100px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw) translateY(100vh);
    opacity: 0;
  }
}

/* 添加一些额外的视觉效果 */
.starfield::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(ellipse at 20% 50%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(191, 0, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(255, 0, 128, 0.1) 0%, transparent 50%);
  pointer-events: none;
}
</style>
