# HeroIcons

This package contains icon data for HeroIcons icon set.

Files:

-   Icon data is stored in `icons.json` in `IconifyJSON` format. See [IconifyJSON documentation](https://iconify.design/docs/types/iconify-json.html).
-   Icon set information is stored in `info.json` in `IconifyInfo` format. See [IconifyInfo documentation](https://iconify.design/docs/types/iconify-info.html).

## Installation

```bash
npm install @iconify-json/heroicons --save-dev
```

## Usage

Icon data can be used with many tools and components, see [Iconify documentation](https://iconify.design/docs/usage/).

To convert to SVG, you can use [Iconify Utils](https://iconify.design/docs/libraries/utils/examples/export-svgs-from-icon-set.html) for basic usage or [Iconify Tools](https://iconify.design/docs/libraries/tools/).
