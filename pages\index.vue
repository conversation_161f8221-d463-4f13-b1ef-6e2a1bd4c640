<template>
  <div class="relative">
    <!-- 英雄区域 -->
    <section class="min-h-screen flex items-center justify-center px-4">
      <div class="text-center max-w-4xl mx-auto">
        <!-- 主标题 -->
        <h1 class="text-6xl md:text-8xl font-bold mb-6 neon-glow text-transparent bg-clip-text bg-gradient-to-r from-neon-blue via-neon-purple to-neon-pink animate-pulse-slow">
          霓虹夜空
        </h1>
        
        <!-- 副标题 -->
        <p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
          记录那些<span class="text-neon-pink">难以愈合的痛</span>，<br>
          <span class="text-neon-blue">磕磕碰碰</span>的瞬间，<br>
          以及<span class="text-neon-green">曾经的怦然心动</span>
        </p>
        
        <!-- 描述文字 -->
        <p class="text-lg text-gray-400 mb-12 max-w-2xl mx-auto">
          在这个私密的情绪空间里，每一段文字、每一张图片、每一段录音，
          都会在你的专属夜空中生成一颗可视化的星星。
          悲伤有一千万种，但孤独的人都相同。
        </p>
        
        <!-- 行动按钮 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <NuxtLink
            v-if="!user"
            to="/auth/register"
            class="btn-neon text-lg px-8 py-4"
          >
            <Icon name="heroicons:sparkles" class="w-5 h-5 mr-2" />
            开始记录星辰
          </NuxtLink>
          
          <NuxtLink
            v-else
            to="/stars"
            class="btn-neon text-lg px-8 py-4"
          >
            <Icon name="heroicons:star" class="w-5 h-5 mr-2" />
            查看我的夜空
          </NuxtLink>
          
          <NuxtLink
            to="/galaxy"
            class="btn-secondary text-lg px-8 py-4"
          >
            <Icon name="heroicons:globe-alt" class="w-5 h-5 mr-2" />
            探索银河共鸣
          </NuxtLink>
        </div>
      </div>
      
      <!-- 滚动提示 -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <Icon name="heroicons:chevron-down" class="w-6 h-6 text-gray-400" />
      </div>
    </section>

    <!-- 功能介绍区域 -->
    <section class="py-20 px-4">
      <div class="container mx-auto max-w-6xl">
        <h2 class="text-4xl font-bold text-center mb-16 neon-glow text-neon-blue">
          探索情绪的宇宙
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
          <!-- 记忆星辰功能 -->
          <div class="neon-card p-8 group hover:scale-105 transition-transform duration-300">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 rounded-full bg-gradient-to-r from-neon-blue to-neon-purple flex items-center justify-center mr-4">
                <Icon name="heroicons:star" class="w-6 h-6 text-white" />
              </div>
              <h3 class="text-2xl font-bold text-neon-blue">记忆星辰</h3>
            </div>
            
            <p class="text-gray-300 mb-6 leading-relaxed">
              将你的情绪记录转化为夜空中的星辰。每一段文字、图片或录音都会生成独特的星星，
              你可以调节它们的亮度，甚至将沉重的记忆封存在时间胶囊中。
            </p>
            
            <div class="space-y-3">
              <div class="flex items-center text-sm text-gray-400">
                <Icon name="heroicons:check" class="w-4 h-4 text-neon-green mr-2" />
                私密的个人情绪记录空间
              </div>
              <div class="flex items-center text-sm text-gray-400">
                <Icon name="heroicons:check" class="w-4 h-4 text-neon-green mr-2" />
                可视化的星辰展示
              </div>
              <div class="flex items-center text-sm text-gray-400">
                <Icon name="heroicons:check" class="w-4 h-4 text-neon-green mr-2" />
                情绪淡化与时间胶囊
              </div>
            </div>
          </div>
          
          <!-- 遥远共鸣功能 -->
          <div class="neon-card p-8 group hover:scale-105 transition-transform duration-300">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 rounded-full bg-gradient-to-r from-neon-purple to-neon-pink flex items-center justify-center mr-4">
                <Icon name="heroicons:heart" class="w-6 h-6 text-white" />
              </div>
              <h3 class="text-2xl font-bold text-neon-purple">遥远共鸣</h3>
            </div>
            
            <p class="text-gray-300 mb-6 leading-relaxed">
              匿名地将你的情绪投放到公共银河中，与其他孤独的灵魂产生共鸣。
              没有评论，没有社交压力，只有纯粹的理解与陪伴。
            </p>
            
            <div class="space-y-3">
              <div class="flex items-center text-sm text-gray-400">
                <Icon name="heroicons:check" class="w-4 h-4 text-neon-green mr-2" />
                完全匿名的情绪分享
              </div>
              <div class="flex items-center text-sm text-gray-400">
                <Icon name="heroicons:check" class="w-4 h-4 text-neon-green mr-2" />
                光脉冲共鸣机制
              </div>
              <div class="flex items-center text-sm text-gray-400">
                <Icon name="heroicons:check" class="w-4 h-4 text-neon-green mr-2" />
                被看见的温暖慰藉
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据区域 -->
    <section class="py-20 px-4 bg-gradient-to-r from-gray-900/50 to-gray-800/50">
      <div class="container mx-auto max-w-4xl text-center">
        <h2 class="text-3xl font-bold mb-12 text-white">
          在这里，你并不孤单
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="space-y-2">
            <div class="text-4xl font-bold text-neon-blue neon-glow">
              {{ stats.totalStars.toLocaleString() }}
            </div>
            <div class="text-gray-400">记忆星辰</div>
          </div>
          
          <div class="space-y-2">
            <div class="text-4xl font-bold text-neon-purple neon-glow">
              {{ stats.totalResonances.toLocaleString() }}
            </div>
            <div class="text-gray-400">共鸣次数</div>
          </div>
          
          <div class="space-y-2">
            <div class="text-4xl font-bold text-neon-pink neon-glow">
              {{ stats.activeUsers.toLocaleString() }}
            </div>
            <div class="text-gray-400">活跃用户</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useAuthStore } from '~/stores/auth'

const authStore = useAuthStore()
const { user } = storeToRefs(authStore)

// 模拟统计数据
const stats = ref({
  totalStars: 12847,
  totalResonances: 45623,
  activeUsers: 3291
})

// 页面元数据
useHead({
  title: '霓虹夜空 - 记录情绪，寻找共鸣',
  meta: [
    { name: 'description', content: '一个私密的个人情绪记录与封存空间，在这里记录你的记忆星辰，寻找遥远的共鸣。' }
  ]
})
</script>

<style scoped>
.btn-secondary {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-300 border-2 border-gray-600 text-gray-300 hover:border-neon-purple hover:text-neon-purple;
}

.btn-secondary:hover {
  box-shadow: 0 0 20px rgba(191, 0, 255, 0.3);
}
</style>
