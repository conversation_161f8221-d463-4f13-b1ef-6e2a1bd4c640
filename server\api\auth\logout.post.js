import prisma from '~/lib/prisma.js'
import { getUserFromRequest } from '~/utils/auth.js'

export default defineEventHandler(async (event) => {
  try {
    const user = await getUserFromRequest(event)
    
    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权'
      })
    }

    // 获取当前 token
    const token = getCookie(event, 'auth-token') || getHeader(event, 'authorization')?.replace('Bearer ', '')
    
    if (token) {
      // 删除会话记录
      await prisma.session.deleteMany({
        where: {
          userId: user.id,
          token
        }
      })
    }

    return {
      success: true,
      message: '登出成功'
    }
  } catch (error) {
    console.error('Logout error:', error)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    })
  }
})
