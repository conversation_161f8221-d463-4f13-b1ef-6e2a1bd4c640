"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0; // https://nuxt.com/docs/api/configuration/nuxt-config
var _default = exports.default = defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  // 模块配置
  modules: [
  '@nuxtjs/tailwindcss',
  '@pinia/nuxt',
  '@vueuse/nuxt',
  '@nuxtjs/color-mode',
  '@nuxtjs/google-fonts',
  '@nuxt/icon'],


  // CSS 配置
  css: ['~/assets/css/main.css'],

  // 颜色模式配置
  colorMode: {
    preference: 'dark',
    fallback: 'dark',
    classSuffix: ''
  },

  // Google Fonts 配置
  googleFonts: {
    families: {
      'Inter': [400, 500, 600, 700],
      'JetBrains Mono': [400, 500]
    },
    display: 'swap'
  },

  // TypeScript 配置
  typescript: {
    strict: true
  },

  // 运行时配置
  runtimeConfig: {
    // 私有密钥（仅在服务端可用）
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    databaseUrl: process.env.DATABASE_URL || 'file:./dev.db',

    // 公共密钥（客户端也可用）
    public: {
      appName: '霓虹夜空',
      appDescription: '记录情绪，寻找共鸣的私密空间'
    }
  },

  // 服务端渲染配置
  ssr: true,

  // 实验性功能
  experimental: {
    payloadExtraction: false
  }
}); /* v9-780a390059e4ce43 */
