import prisma from '~/lib/prisma.js'
import { comparePassword, generateToken } from '~/utils/auth.js'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { login, password } = body

    // 验证输入
    if (!login || !password) {
      throw createError({
        statusCode: 400,
        statusMessage: '请输入用户名/邮箱和密码'
      })
    }

    // 查找用户（支持邮箱或用户名登录）
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { email: login },
          { username: login }
        ]
      }
    })

    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户名/邮箱或密码错误'
      })
    }

    // 验证密码
    const isPasswordValid = await comparePassword(password, user.password)
    if (!isPasswordValid) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户名/邮箱或密码错误'
      })
    }

    // 生成 JWT token
    const token = generateToken({ userId: user.id })

    // 创建会话
    await prisma.session.create({
      data: {
        userId: user.id,
        token,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后过期
      }
    })

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = user

    return {
      success: true,
      data: {
        user: userWithoutPassword,
        token
      },
      message: '登录成功'
    }
  } catch (error) {
    console.error('Login error:', error)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    })
  }
})
