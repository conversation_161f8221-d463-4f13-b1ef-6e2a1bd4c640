<template>
  <div class="min-h-screen flex items-center justify-center px-4">
    <div class="max-w-md w-full">
      <!-- 标题 -->
      <div class="text-center mb-8">
        <NuxtLink to="/" class="inline-flex items-center space-x-2 mb-6">
          <div class="w-10 h-10 rounded-full bg-gradient-to-r from-neon-blue to-neon-purple flex items-center justify-center">
            <Icon name="heroicons:sparkles" class="w-6 h-6 text-white" />
          </div>
          <span class="text-2xl font-bold neon-glow text-neon-blue">
            霓虹夜空
          </span>
        </NuxtLink>
        
        <h1 class="text-3xl font-bold text-white mb-2">
          欢迎回来
        </h1>
        <p class="text-gray-400">
          登录到你的星辰夜空
        </p>
      </div>

      <!-- 登录表单 -->
      <form @submit.prevent="handleLogin" class="neon-card p-8 space-y-6">
        <!-- 用户名/邮箱 -->
        <div>
          <label for="login" class="block text-sm font-medium text-gray-300 mb-2">
            用户名或邮箱
          </label>
          <input
            id="login"
            v-model="form.login"
            type="text"
            required
            class="input-neon"
            placeholder="请输入用户名或邮箱"
            :disabled="isLoading"
          />
        </div>

        <!-- 密码 -->
        <div>
          <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
            密码
          </label>
          <div class="relative">
            <input
              id="password"
              v-model="form.password"
              :type="showPassword ? 'text' : 'password'"
              required
              class="input-neon pr-12"
              placeholder="请输入密码"
              :disabled="isLoading"
            />
            <button
              type="button"
              @click="showPassword = !showPassword"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            >
              <Icon 
                :name="showPassword ? 'heroicons:eye-slash' : 'heroicons:eye'" 
                class="w-5 h-5" 
              />
            </button>
          </div>
        </div>

        <!-- 记住我 -->
        <div class="flex items-center justify-between">
          <label class="flex items-center">
            <input
              v-model="form.rememberMe"
              type="checkbox"
              class="w-4 h-4 text-neon-blue bg-gray-800 border-gray-600 rounded focus:ring-neon-blue focus:ring-2"
            />
            <span class="ml-2 text-sm text-gray-300">记住我</span>
          </label>
          
          <NuxtLink 
            to="/auth/forgot-password" 
            class="text-sm text-neon-blue hover:text-neon-purple transition-colors"
          >
            忘记密码？
          </NuxtLink>
        </div>

        <!-- 错误信息 -->
        <div v-if="error" class="p-3 rounded-lg bg-red-900/20 border border-red-500/30">
          <p class="text-red-400 text-sm">{{ error }}</p>
        </div>

        <!-- 登录按钮 -->
        <button
          type="submit"
          :disabled="isLoading"
          class="w-full btn-neon py-3 text-lg"
        >
          <Icon 
            v-if="isLoading" 
            name="heroicons:arrow-path" 
            class="w-5 h-5 mr-2 animate-spin" 
          />
          <Icon 
            v-else 
            name="heroicons:arrow-right-on-rectangle" 
            class="w-5 h-5 mr-2" 
          />
          {{ isLoading ? '登录中...' : '登录' }}
        </button>

        <!-- 分割线 -->
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-700"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-dark-bg text-gray-400">或者</span>
          </div>
        </div>

        <!-- 注册链接 -->
        <div class="text-center">
          <p class="text-gray-400">
            还没有账号？
            <NuxtLink 
              to="/auth/register" 
              class="text-neon-blue hover:text-neon-purple transition-colors font-medium"
            >
              立即注册
            </NuxtLink>
          </p>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '~/stores/auth'
import { useNotificationStore } from '~/stores/notification'

// 页面元数据
definePageMeta({
  middleware: 'guest',
  layout: false
})

useHead({
  title: '登录 - 霓虹夜空'
})

const authStore = useAuthStore()
const notificationStore = useNotificationStore()
const { isLoading } = storeToRefs(authStore)

// 表单数据
const form = reactive({
  login: '',
  password: '',
  rememberMe: false
})

const showPassword = ref(false)
const error = ref('')

// 处理登录
const handleLogin = async () => {
  error.value = ''
  
  if (!form.login || !form.password) {
    error.value = '请填写所有必填字段'
    return
  }

  const result = await authStore.login({
    login: form.login,
    password: form.password
  })

  if (result.success) {
    notificationStore.success('登录成功，欢迎回来！')
    await navigateTo('/stars')
  } else {
    error.value = result.error
  }
}
</script>
