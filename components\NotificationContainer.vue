<template>
  <div class="fixed top-4 right-4 z-50 space-y-2">
    <TransitionGroup
      name="notification"
      tag="div"
      class="space-y-2"
    >
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="neon-card p-4 max-w-sm"
        :class="getNotificationClass(notification.type)"
      >
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <Icon :name="getNotificationIcon(notification.type)" class="w-5 h-5" />
          </div>
          <div class="flex-1">
            <h4 v-if="notification.title" class="font-medium text-sm">
              {{ notification.title }}
            </h4>
            <p class="text-sm opacity-90">
              {{ notification.message }}
            </p>
          </div>
          <button
            @click="removeNotification(notification.id)"
            class="flex-shrink-0 text-gray-400 hover:text-white transition-colors"
          >
            <Icon name="heroicons:x-mark" class="w-4 h-4" />
          </button>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup>
import { useNotificationStore } from '~/stores/notification'

const notificationStore = useNotificationStore()
const { notifications } = storeToRefs(notificationStore)
const { removeNotification } = notificationStore

const getNotificationClass = (type) => {
  const classes = {
    success: 'border-neon-green text-neon-green',
    error: 'border-neon-pink text-neon-pink',
    warning: 'border-yellow-400 text-yellow-400',
    info: 'border-neon-blue text-neon-blue',
    resonance: 'border-neon-purple text-neon-purple'
  }
  return classes[type] || classes.info
}

const getNotificationIcon = (type) => {
  const icons = {
    success: 'heroicons:check-circle',
    error: 'heroicons:x-circle',
    warning: 'heroicons:exclamation-triangle',
    info: 'heroicons:information-circle',
    resonance: 'heroicons:heart'
  }
  return icons[type] || icons.info
}
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
