import { defineStore } from 'pinia'

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notifications: []
  }),

  actions: {
    addNotification({ type = 'info', title, message, duration = 5000 }) {
      const id = Date.now() + Math.random()
      
      const notification = {
        id,
        type,
        title,
        message,
        duration
      }

      this.notifications.push(notification)

      // 自动移除通知
      if (duration > 0) {
        setTimeout(() => {
          this.removeNotification(id)
        }, duration)
      }

      return id
    },

    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },

    clearAll() {
      this.notifications = []
    },

    // 便捷方法
    success(message, title = '成功') {
      return this.addNotification({ type: 'success', title, message })
    },

    error(message, title = '错误') {
      return this.addNotification({ type: 'error', title, message, duration: 8000 })
    },

    warning(message, title = '警告') {
      return this.addNotification({ type: 'warning', title, message })
    },

    info(message, title = '提示') {
      return this.addNotification({ type: 'info', title, message })
    },

    resonance(message, title = '共鸣') {
      return this.addNotification({ 
        type: 'resonance', 
        title, 
        message, 
        duration: 6000 
      })
    }
  }
})
