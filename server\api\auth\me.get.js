import { getUserFromRequest } from '~/utils/auth.js'

export default defineEventHandler(async (event) => {
  try {
    const user = await getUserFromRequest(event)
    
    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权'
      })
    }

    return {
      success: true,
      data: {
        user
      }
    }
  } catch (error) {
    console.error('Get user error:', error)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    })
  }
})
