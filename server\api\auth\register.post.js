import prisma from '~/lib/prisma.js'
import { hashPassword, generateToken, isValidEmail, isValidUsername, isValidPassword } from '~/utils/auth.js'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { email, username, password, confirmPassword } = body

    // 验证输入
    if (!email || !username || !password || !confirmPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: '所有字段都是必填的'
      })
    }

    if (password !== confirmPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: '两次输入的密码不一致'
      })
    }

    if (!isValidEmail(email)) {
      throw createError({
        statusCode: 400,
        statusMessage: '邮箱格式不正确'
      })
    }

    if (!isValidUsername(username)) {
      throw createError({
        statusCode: 400,
        statusMessage: '用户名只能包含字母、数字、下划线，长度3-20位'
      })
    }

    if (!isValidPassword(password)) {
      throw createError({
        statusCode: 400,
        statusMessage: '密码至少8位，需包含字母和数字'
      })
    }

    // 检查邮箱是否已存在
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUserByEmail) {
      throw createError({
        statusCode: 400,
        statusMessage: '该邮箱已被注册'
      })
    }

    // 检查用户名是否已存在
    const existingUserByUsername = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUserByUsername) {
      throw createError({
        statusCode: 400,
        statusMessage: '该用户名已被使用'
      })
    }

    // 创建用户
    const hashedPassword = await hashPassword(password)
    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: hashedPassword
      },
      select: {
        id: true,
        email: true,
        username: true,
        avatar: true,
        bio: true,
        createdAt: true
      }
    })

    // 生成 JWT token
    const token = generateToken({ userId: user.id })

    // 创建会话
    await prisma.session.create({
      data: {
        userId: user.id,
        token,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后过期
      }
    })

    return {
      success: true,
      data: {
        user,
        token
      },
      message: '注册成功'
    }
  } catch (error) {
    console.error('Register error:', error)
    
    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    })
  }
})
