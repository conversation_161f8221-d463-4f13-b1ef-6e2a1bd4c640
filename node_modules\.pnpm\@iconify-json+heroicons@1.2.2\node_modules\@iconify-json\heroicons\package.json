{"name": "@iconify-json/heroicons", "description": "HeroIcons icon set in Iconify JSON format", "version": "1.2.2", "iconSetVersion": "2.2.0", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "homepage": "https://icon-sets.iconify.design/heroicons/", "bugs": "https://github.com/iconify/icon-sets/issues", "license": "MIT", "exports": {"./*": "./*", ".": {"types": "./index.d.ts", "require": "./index.js", "import": "./index.mjs"}, "./icons.json": "./icons.json", "./info.json": "./info.json", "./metadata.json": "./metadata.json"}, "iconSet": {"icons": "icons.json", "info": "info.json", "metadata": "metadata.json"}, "dependencies": {"@iconify/types": "*"}}