{"name": "@nuxt/icon", "version": "1.15.0", "license": "MIT", "type": "module", "repository": "nuxt/icon", "exports": {".": "./dist/module.mjs", "./module": "./dist/module.mjs", "./*": "./dist/*"}, "main": "./dist/module.mjs", "module": "./dist/module.mjs", "types": "./dist/types.d.mts", "files": ["dist"], "dependencies": {"@iconify/collections": "^1.0.563", "@iconify/types": "^2.0.0", "@iconify/utils": "^2.3.0", "@iconify/vue": "^5.0.0", "@nuxt/devtools-kit": "^2.5.0", "@nuxt/kit": "^3.17.5", "consola": "^3.4.2", "local-pkg": "^1.1.1", "mlly": "^1.7.4", "ohash": "^2.0.11", "pathe": "^2.0.3", "picomatch": "^4.0.2", "std-env": "^3.9.0", "tinyglobby": "^0.2.14"}, "devDependencies": {"@iconify-json/fluent-emoji-high-contrast": "^1.2.3", "@iconify-json/logos": "^1.2.4", "@iconify-json/ph": "^1.2.2", "@iconify-json/simple-icons": "^1.2.40", "@iconify-json/solar": "^1.2.2", "@iconify-json/uil": "^1.2.3", "@nuxt/devtools": "^2.5.0", "@nuxt/eslint-config": "^1.4.1", "@nuxt/module-builder": "^1.0.1", "@nuxt/schema": "^3.17.5", "@nuxt/test-utils": "^3.19.1", "@types/node": "^24.0.7", "@types/picomatch": "^4.0.0", "@unocss/nuxt": "^66.3.2", "bumpp": "^10.2.0", "changelogen": "^0.6.1", "eslint": "^9.30.0", "nuxt": "^3.17.5", "nuxthub": "^0.9.2", "prettier": "^3.6.2", "typescript": "~5.8.3", "untyped": "^2.0.0", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}, "resolutions": {"nuxt": "^3.17.5", "vite": "^7.0.0"}, "scripts": {"build": "nuxt-module-build prepare && nuxt-module-build build", "play": "nuxi dev playground", "play:build": "nuxi build playground", "play:deploy": "nr -C playground deploy", "dev": "nuxi dev playground", "dev:prepare": "nuxt-module-build build --stub && nuxt-module-build prepare && nuxi prepare playground", "lint": "eslint .", "typecheck": "vue-tsc --noEmit", "release": "bumpp && pnpm publish", "test:playground": "pnpm -C playground run test", "test:unit": "vitest", "test": "pnpm run test:unit --run && pnpm run test:playground --run"}}