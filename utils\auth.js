import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'

// JWT 工具函数
export const generateToken = (payload) => {
  const config = useRuntimeConfig()
  return jwt.sign(payload, config.jwtSecret, { expiresIn: '7d' })
}

export const verifyToken = (token) => {
  const config = useRuntimeConfig()
  try {
    return jwt.verify(token, config.jwtSecret)
  } catch (error) {
    return null
  }
}

// 密码工具函数
export const hashPassword = async (password) => {
  const saltRounds = 12
  return await bcrypt.hash(password, saltRounds)
}

export const comparePassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword)
}

// 验证邮箱格式
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证用户名格式
export const isValidUsername = (username) => {
  // 用户名只能包含字母、数字、下划线，长度3-20
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
  return usernameRegex.test(username)
}

// 验证密码强度
export const isValidPassword = (password) => {
  // 密码至少8位，包含字母和数字
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/
  return passwordRegex.test(password)
}

// 从请求中提取用户信息
export const getUserFromRequest = async (event) => {
  const token = getCookie(event, 'auth-token') || getHeader(event, 'authorization')?.replace('Bearer ', '')
  
  if (!token) {
    return null
  }

  const payload = verifyToken(token)
  if (!payload) {
    return null
  }

  try {
    const { default: prisma } = await import('~/lib/prisma.js')
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        avatar: true,
        bio: true,
        createdAt: true
      }
    })
    return user
  } catch (error) {
    console.error('Error fetching user:', error)
    return null
  }
}

// 生成随机颜色
export const generateStarColor = (emotion) => {
  const colors = {
    JOY: '#FFD700',       // 金色
    SADNESS: '#4169E1',   // 蓝色
    ANGER: '#FF4500',     // 橙红色
    FEAR: '#8B008B',      // 深紫色
    LOVE: '#FF69B4',      // 粉色
    HOPE: '#00FF7F',      // 春绿色
    NOSTALGIA: '#DDA0DD', // 淡紫色
    ANXIETY: '#FF6347',   // 番茄色
    PEACE: '#87CEEB',     // 天蓝色
    NEUTRAL: '#FFFFFF'    // 白色
  }
  return colors[emotion] || colors.NEUTRAL
}

// 生成随机位置
export const generateRandomPosition = () => {
  return {
    x: Math.random() * 100,
    y: Math.random() * 100,
    z: Math.random() * 10 // 深度层次
  }
}
