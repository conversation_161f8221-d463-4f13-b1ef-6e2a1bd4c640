{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/icon": "^1.15.0", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.1", "@prisma/client": "^6.11.1", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@vueuse/core": "^13.5.0", "@vueuse/nuxt": "^13.5.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "nuxt": "^3.17.6", "pinia": "^3.0.3", "prisma": "^6.11.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184", "devDependencies": {"@iconify-json/heroicons": "^1.2.2"}}