import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isLoading: false
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
    isLoggedIn: (state) => !!state.user
  },

  actions: {
    async login(credentials) {
      this.isLoading = true
      try {
        const { data } = await $fetch('/api/auth/login', {
          method: 'POST',
          body: credentials
        })

        this.token = data.token
        this.user = data.user

        // 保存到本地存储
        const tokenCookie = useCookie('auth-token', {
          default: () => null,
          maxAge: 60 * 60 * 24 * 7 // 7 days
        })
        tokenCookie.value = data.token

        return { success: true, data }
      } catch (error) {
        console.error('Login error:', error)
        return { 
          success: false, 
          error: error.data?.message || '登录失败，请重试' 
        }
      } finally {
        this.isLoading = false
      }
    },

    async register(userData) {
      this.isLoading = true
      try {
        const { data } = await $fetch('/api/auth/register', {
          method: 'POST',
          body: userData
        })

        this.token = data.token
        this.user = data.user

        // 保存到本地存储
        const tokenCookie = useCookie('auth-token', {
          default: () => null,
          maxAge: 60 * 60 * 24 * 7 // 7 days
        })
        tokenCookie.value = data.token

        return { success: true, data }
      } catch (error) {
        console.error('Register error:', error)
        return { 
          success: false, 
          error: error.data?.message || '注册失败，请重试' 
        }
      } finally {
        this.isLoading = false
      }
    },

    async logout() {
      try {
        // 调用服务端登出接口
        await $fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.token}`
          }
        })
      } catch (error) {
        console.error('Logout error:', error)
      } finally {
        // 清除本地状态
        this.user = null
        this.token = null

        // 清除 cookie
        const tokenCookie = useCookie('auth-token')
        tokenCookie.value = null

        // 重定向到首页
        await navigateTo('/')
      }
    },

    async fetchUser() {
      if (!this.token) return

      try {
        const { data } = await $fetch('/api/auth/me', {
          headers: {
            Authorization: `Bearer ${this.token}`
          }
        })

        this.user = data.user
        return { success: true, data }
      } catch (error) {
        console.error('Fetch user error:', error)
        // Token 可能已过期，清除认证状态
        this.logout()
        return { success: false, error }
      }
    },

    async updateProfile(profileData) {
      this.isLoading = true
      try {
        const { data } = await $fetch('/api/auth/profile', {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${this.token}`
          },
          body: profileData
        })

        this.user = { ...this.user, ...data.user }
        return { success: true, data }
      } catch (error) {
        console.error('Update profile error:', error)
        return { 
          success: false, 
          error: error.data?.message || '更新失败，请重试' 
        }
      } finally {
        this.isLoading = false
      }
    },

    // 初始化认证状态
    async initAuth() {
      const tokenCookie = useCookie('auth-token')
      if (tokenCookie.value) {
        this.token = tokenCookie.value
        await this.fetchUser()
      }
    }
  }
})
