// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/7/9 11:07:35
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["D:/桌面/AI编程/web应用/Neon-and-Night/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/桌面/AI编程/web应用/Neon-and-Night/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/桌面/AI编程/web应用/Neon-and-Night/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/桌面/AI编程/web应用/Neon-and-Night/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/桌面/AI编程/web应用/Neon-and-Night/plugins/**/*.{js,ts,mjs}","D:/桌面/AI编程/web应用/Neon-and-Night/composables/**/*.{js,ts,mjs}","D:/桌面/AI编程/web应用/Neon-and-Night/utils/**/*.{js,ts,mjs}","D:/桌面/AI编程/web应用/Neon-and-Night/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/桌面/AI编程/web应用/Neon-and-Night/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","D:/桌面/AI编程/web应用/Neon-and-Night/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","D:/桌面/AI编程/web应用/Neon-and-Night/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;