// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  avatar    String?
  bio       String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  stars      Star[]
  resonances Resonance[]
  sessions   Session[]

  @@map("users")
}

// 会话模型（用于认证）
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// 记忆星辰模型
model Star {
  id          String    @id @default(cuid())
  userId      String
  title       String?
  content     String
  type        StarType  @default(TEXT)
  mediaUrl    String?   // 图片或音频文件URL
  emotion     Emotion   @default(NEUTRAL)
  brightness  Float     @default(1.0) // 星辰亮度 0.0-1.0
  isSealed    Boolean   @default(false) // 是否被封存
  sealedUntil DateTime? // 封存到期时间
  position    Json?     // 在夜空中的位置坐标
  color       String?   // 星辰颜色
  size        Float     @default(1.0) // 星辰大小
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联关系
  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  resonances Resonance[]

  @@map("stars")
}

// 共鸣模型
model Resonance {
  id        String   @id @default(cuid())
  starId    String
  userId    String
  type      ResonanceType @default(PULSE)
  message   String?  // 可选的共鸣消息
  isAnonymous Boolean @default(true)
  createdAt DateTime @default(now())

  // 关联关系
  star Star @relation(fields: [starId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 防止重复共鸣
  @@unique([starId, userId])
  @@map("resonances")
}

// 银河中的匿名情绪光点
model GalaxyEmotion {
  id        String   @id @default(cuid())
  content   String
  emotion   Emotion
  intensity Float    @default(0.5) // 情绪强度 0.0-1.0
  position  Json     // 在银河中的位置
  color     String   // 光点颜色
  size      Float    @default(1.0)
  expiresAt DateTime // 光点存在时间
  createdAt DateTime @default(now())

  // 关联的共鸣
  resonances GalaxyResonance[]

  @@map("galaxy_emotions")
}

// 银河共鸣模型
model GalaxyResonance {
  id              String   @id @default(cuid())
  galaxyEmotionId String
  userId          String
  type            ResonanceType @default(PULSE)
  createdAt       DateTime @default(now())

  // 关联关系
  galaxyEmotion GalaxyEmotion @relation(fields: [galaxyEmotionId], references: [id], onDelete: Cascade)

  // 防止重复共鸣
  @@unique([galaxyEmotionId, userId])
  @@map("galaxy_resonances")
}

// 枚举类型
enum StarType {
  TEXT
  IMAGE
  AUDIO
  MIXED
}

enum Emotion {
  JOY       // 喜悦
  SADNESS   // 悲伤
  ANGER     // 愤怒
  FEAR      // 恐惧
  LOVE      // 爱
  HOPE      // 希望
  NOSTALGIA // 怀念
  ANXIETY   // 焦虑
  PEACE     // 平静
  NEUTRAL   // 中性
}

enum ResonanceType {
  PULSE     // 光脉冲
  WARMTH    // 温暖
  COMFORT   // 安慰
  UNDERSTANDING // 理解
}
